#!/usr/bin/env python3
"""
测试所有修复
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_all_fixes():
    """测试所有修复"""
    print("🧪 测试所有修复...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 1. 测试系统设置 - 默认模型显示
        print("\n📋 测试系统设置...")
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        if response.status_code == 200:
            settings = response.json()
            settings_map = {s['key']: s['value'] for s in settings}
            default_model_id = settings_map.get('default_model_id')
            
            if default_model_id:
                # 获取模型列表验证
                models_response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
                if models_response.status_code == 200:
                    models = models_response.json()
                    model = next((m for m in models if m['id'] == int(default_model_id)), None)
                    if model:
                        print(f"✅ 默认模型设置正确: {model['display_name']} (ID: {default_model_id})")
                    else:
                        print(f"❌ 找不到默认模型 ID: {default_model_id}")
        
        # 2. 测试对话历史保留天数
        print("\n🕒 测试对话历史保留天数...")
        chat_retention = settings_map.get('chat_retention_days')
        print(f"当前对话历史保留天数: {chat_retention}")
        if chat_retention == '0':
            print("✅ 对话历史保留天数设置为永久（0天）")
        else:
            print(f"⚠️ 对话历史保留天数设置为: {chat_retention}天")
        
        # 3. 测试AI模型max_tokens设置
        print("\n🤖 测试AI模型max_tokens设置...")
        response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ 获取AI模型列表成功: {len(models)} 个模型")
            
            # 找一个模型测试
            if models:
                test_model = models[0]
                print(f"测试模型: {test_model['display_name']} (ID: {test_model['id']})")
                print(f"当前max_tokens: {test_model['max_tokens']}")
                
                # 测试设置为无限制
                print("  🔧 测试设置为无限制...")
                update_data = {"max_tokens": None}
                response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model['id']}", json=update_data, headers=headers)
                if response.status_code == 200:
                    print("  ✅ 设置无限制成功")
                    
                    # 验证
                    response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
                    if response.status_code == 200:
                        updated_models = response.json()
                        updated_model = next((m for m in updated_models if m['id'] == test_model['id']), None)
                        if updated_model and updated_model['max_tokens'] is None:
                            print("  ✅ 验证成功：max_tokens已设置为无限制(null)")
                        else:
                            print(f"  ❌ 验证失败：max_tokens = {updated_model['max_tokens'] if updated_model else 'None'}")
                else:
                    print(f"  ❌ 设置无限制失败: {response.status_code}")
                
                # 测试设置为自定义值
                print("  🔧 测试设置为自定义值...")
                update_data = {"max_tokens": 4000}
                response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model['id']}", json=update_data, headers=headers)
                if response.status_code == 200:
                    print("  ✅ 设置自定义值成功")
                    
                    # 验证
                    response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
                    if response.status_code == 200:
                        updated_models = response.json()
                        updated_model = next((m for m in updated_models if m['id'] == test_model['id']), None)
                        if updated_model and updated_model['max_tokens'] == 4000:
                            print("  ✅ 验证成功：max_tokens已设置为4000")
                        else:
                            print(f"  ❌ 验证失败：max_tokens = {updated_model['max_tokens'] if updated_model else 'None'}")
                else:
                    print(f"  ❌ 设置自定义值失败: {response.status_code}")
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_all_fixes()
