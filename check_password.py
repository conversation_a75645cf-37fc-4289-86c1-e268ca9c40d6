#!/usr/bin/env python3
import requests
from passlib.context import CryptContext

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 先用管理员登录查看testuser的密码哈希
login_data = {'username': 'admin', 'password': 'admin123'}
response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
if response.status_code == 200:
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取用户列表
    users_response = requests.get('http://localhost:8000/api/admin/users', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()
        for user in users:
            if user['username'] == 'testuser':
                print(f"找到testuser:")
                print(f"  - ID: {user['id']}")
                print(f"  - 用户名: {user['username']}")
                print(f"  - 状态: {user['status']}")
                print(f"  - 密码哈希: {user.get('password_hash', '未显示')}")
                
                # 测试密码验证
                test_password = "test123"
                if 'password_hash' in user:
                    try:
                        is_valid = pwd_context.verify(test_password, user['password_hash'])
                        print(f"  - 密码'{test_password}'验证: {'通过' if is_valid else '失败'}")
                    except Exception as e:
                        print(f"  - 密码验证错误: {e}")
                break
        else:
            print("未找到testuser")
    else:
        print('获取用户列表失败')
else:
    print('管理员登录失败')
