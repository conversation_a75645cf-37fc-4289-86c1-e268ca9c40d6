"""
管理员相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel


class UserAdminResponse(BaseModel):
    """管理员视角的用户响应模式"""
    id: int
    username: str
    email: str
    display_name: Optional[str] = None
    is_admin: bool
    status: str
    last_login_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserStatusUpdate(BaseModel):
    """用户状态更新模式"""
    status: str  # active, disabled, pending


class UserCreateRequest(BaseModel):
    """创建用户请求模式"""
    username: str
    email: str
    password: str
    display_name: Optional[str] = None
    is_admin: bool = False


class UserUpdateRequest(BaseModel):
    """更新用户请求模式"""
    username: Optional[str] = None
    email: Optional[str] = None
    display_name: Optional[str] = None
    is_admin: Optional[bool] = None
    status: Optional[str] = None


class UserQuotaUpdate(BaseModel):
    """用户配额更新模式"""
    max_kbs: Optional[int] = None
    max_docs_per_kb: Optional[int] = None
    max_storage_mb: Optional[int] = None


class UserQuotaResponse(BaseModel):
    """用户配额响应模式"""
    user_id: int
    max_kbs: int
    max_docs_per_kb: int
    max_storage_mb: int
    updated_at: datetime

    class Config:
        from_attributes = True


class SystemStats(BaseModel):
    """系统统计信息"""
    total_users: int
    total_knowledge_bases: int
    total_documents: int
    total_storage_mb: float
    total_chat_sessions: int
    total_messages: int
    active_users_today: int


class AIModelAdminResponse(BaseModel):
    """AI模型管理响应模式"""
    id: int
    provider_id: int
    model_name: str
    display_name: str
    is_active: bool
    system_api_key: Optional[str] = None
    allow_system_key_use: bool
    max_tokens: Optional[int] = None
    supports_streaming: bool
    cost_per_1k_tokens: Optional[float] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AIModelUpdateRequest(BaseModel):
    """AI模型更新请求模式"""
    display_name: Optional[str] = None
    is_active: Optional[bool] = None
    system_api_key: Optional[str] = None
    allow_system_key_use: Optional[bool] = None
    max_tokens: Optional[int] = None
    supports_streaming: Optional[bool] = None
    cost_per_1k_tokens: Optional[float] = None


class AIProviderResponse(BaseModel):
    """AI提供商响应模式"""
    id: int
    name: str
    display_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class OperationLogResponse(BaseModel):
    """操作日志响应模式"""
    id: int
    user_id: Optional[int] = None
    action: str
    target_type: Optional[str] = None
    target_id: Optional[int] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class SystemSettingUpdate(BaseModel):
    """系统设置更新模式"""
    value: str
    description: Optional[str] = None


class SystemSettingResponse(BaseModel):
    """系统设置响应模式"""
    key: str
    value: Optional[str] = None
    description: Optional[str] = None
    updated_at: datetime

    class Config:
        from_attributes = True
