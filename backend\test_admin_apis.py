#!/usr/bin/env python3
"""
管理员API测试脚本
测试所有管理员相关的API端点
"""
import requests
import json
import sys
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000/api"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

class AdminAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.admin_token = None
        self.test_user_id = None
        
    def login_admin(self):
        """管理员登录"""
        print("🔐 测试管理员登录...")
        response = self.session.post(f"{BASE_URL}/auth/login", json={
            "username": ADMIN_USERNAME,
            "password": ADMIN_PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            self.admin_token = data["access_token"]
            self.session.headers.update({"Authorization": f"Bearer {self.admin_token}"})
            print("✅ 管理员登录成功")
            return True
        else:
            print(f"❌ 管理员登录失败: {response.status_code} - {response.text}")
            return False
    
    def test_system_stats(self):
        """测试系统统计API"""
        print("\n📊 测试系统统计API...")
        response = self.session.get(f"{BASE_URL}/admin/stats")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 系统统计获取成功:")
            print(f"   总用户数: {data['total_users']}")
            print(f"   总知识库数: {data['total_knowledge_bases']}")
            print(f"   总文档数: {data['total_documents']}")
            print(f"   总聊天会话数: {data['total_chat_sessions']}")
            print(f"   总消息数: {data['total_messages']}")
            print(f"   今日活跃用户: {data['active_users_today']}")
            print(f"   存储使用量: {data['total_storage_mb']} MB")
            return True
        else:
            print(f"❌ 系统统计获取失败: {response.status_code} - {response.text}")
            return False
    
    def test_get_users(self):
        """测试获取用户列表API"""
        print("\n👥 测试获取用户列表API...")
        response = self.session.get(f"{BASE_URL}/admin/users")
        
        if response.status_code == 200:
            users = response.json()
            print(f"✅ 用户列表获取成功，共 {len(users)} 个用户")
            for user in users[:3]:  # 只显示前3个用户
                print(f"   用户: {user['username']} ({user['email']}) - {user['status']}")
            return True
        else:
            print(f"❌ 用户列表获取失败: {response.status_code} - {response.text}")
            return False
    
    def test_create_user(self):
        """测试创建用户API"""
        print("\n➕ 测试创建用户API...")
        test_user_data = {
            "username": f"testuser_{int(datetime.now().timestamp())}",
            "email": f"test_{int(datetime.now().timestamp())}@example.com",
            "password": "testpass123",
            "display_name": "测试用户",
            "is_admin": False
        }
        
        response = self.session.post(f"{BASE_URL}/admin/users", json=test_user_data)
        
        if response.status_code == 200:
            user = response.json()
            self.test_user_id = user["id"]
            print(f"✅ 用户创建成功: {user['username']} (ID: {user['id']})")
            return True
        else:
            print(f"❌ 用户创建失败: {response.status_code} - {response.text}")
            return False
    
    def test_update_user(self):
        """测试更新用户API"""
        if not self.test_user_id:
            print("⚠️  跳过用户更新测试 - 没有测试用户ID")
            return False
            
        print(f"\n✏️  测试更新用户API (ID: {self.test_user_id})...")
        update_data = {
            "display_name": "更新后的测试用户",
            "status": "active"
        }
        
        response = self.session.put(f"{BASE_URL}/admin/users/{self.test_user_id}", json=update_data)
        
        if response.status_code == 200:
            user = response.json()
            print(f"✅ 用户更新成功: {user['display_name']}")
            return True
        else:
            print(f"❌ 用户更新失败: {response.status_code} - {response.text}")
            return False
    
    def test_user_quota(self):
        """测试用户配额管理API"""
        if not self.test_user_id:
            print("⚠️  跳过用户配额测试 - 没有测试用户ID")
            return False
            
        print(f"\n💾 测试用户配额API (ID: {self.test_user_id})...")
        
        # 获取配额
        response = self.session.get(f"{BASE_URL}/admin/users/{self.test_user_id}/quota")
        if response.status_code == 200:
            quota = response.json()
            print(f"✅ 配额获取成功: {quota['max_kbs']} 知识库, {quota['max_storage_mb']} MB")
        else:
            print(f"❌ 配额获取失败: {response.status_code} - {response.text}")
            return False
        
        # 更新配额
        update_quota = {
            "max_kbs": 10,
            "max_docs_per_kb": 200,
            "max_storage_mb": 2048
        }
        
        response = self.session.put(f"{BASE_URL}/admin/users/{self.test_user_id}/quota", json=update_quota)
        if response.status_code == 200:
            quota = response.json()
            print(f"✅ 配额更新成功: {quota['max_kbs']} 知识库, {quota['max_storage_mb']} MB")
            return True
        else:
            print(f"❌ 配额更新失败: {response.status_code} - {response.text}")
            return False
    
    def test_ai_models(self):
        """测试AI模型管理API"""
        print("\n🤖 测试AI模型管理API...")
        
        # 获取AI提供商
        response = self.session.get(f"{BASE_URL}/admin/ai-providers")
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ AI提供商获取成功，共 {len(providers)} 个提供商")
        else:
            print(f"❌ AI提供商获取失败: {response.status_code} - {response.text}")
            return False
        
        # 获取AI模型
        response = self.session.get(f"{BASE_URL}/admin/ai-models")
        if response.status_code == 200:
            models = response.json()
            print(f"✅ AI模型获取成功，共 {len(models)} 个模型")
            
            # 如果有模型，测试更新第一个模型
            if models:
                model_id = models[0]["id"]
                update_data = {
                    "is_active": True,
                    "allow_system_key_use": True
                }
                
                response = self.session.put(f"{BASE_URL}/admin/ai-models/{model_id}", json=update_data)
                if response.status_code == 200:
                    print(f"✅ AI模型更新成功 (ID: {model_id})")
                    return True
                else:
                    print(f"❌ AI模型更新失败: {response.status_code} - {response.text}")
                    return False
            else:
                print("⚠️  没有AI模型可供测试更新")
                return True
        else:
            print(f"❌ AI模型获取失败: {response.status_code} - {response.text}")
            return False
    
    def test_operation_logs(self):
        """测试操作日志API"""
        print("\n📝 测试操作日志API...")
        response = self.session.get(f"{BASE_URL}/admin/logs?limit=5")
        
        if response.status_code == 200:
            logs = response.json()
            print(f"✅ 操作日志获取成功，共 {len(logs)} 条记录")
            for log in logs[:3]:  # 只显示前3条
                print(f"   {log['created_at']}: {log['action']} - {log.get('target_type', 'N/A')}")
            return True
        else:
            print(f"❌ 操作日志获取失败: {response.status_code} - {response.text}")
            return False
    
    def test_system_settings(self):
        """测试系统设置API"""
        print("\n⚙️  测试系统设置API...")
        
        # 获取系统设置
        response = self.session.get(f"{BASE_URL}/admin/settings")
        if response.status_code == 200:
            settings = response.json()
            print(f"✅ 系统设置获取成功，共 {len(settings)} 个设置")
        else:
            print(f"❌ 系统设置获取失败: {response.status_code} - {response.text}")
            return False
        
        # 更新或创建一个测试设置
        test_setting = {
            "value": "test_value_123",
            "description": "测试设置项"
        }
        
        response = self.session.put(f"{BASE_URL}/admin/settings/test_setting", json=test_setting)
        if response.status_code == 200:
            print("✅ 系统设置更新成功")
            return True
        else:
            print(f"❌ 系统设置更新失败: {response.status_code} - {response.text}")
            return False
    
    def cleanup_test_user(self):
        """清理测试用户"""
        if not self.test_user_id:
            return
            
        print(f"\n🗑️  清理测试用户 (ID: {self.test_user_id})...")
        response = self.session.delete(f"{BASE_URL}/admin/users/{self.test_user_id}")
        
        if response.status_code == 200:
            print("✅ 测试用户删除成功")
        else:
            print(f"❌ 测试用户删除失败: {response.status_code} - {response.text}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始管理员API测试...")
        
        if not self.login_admin():
            print("❌ 管理员登录失败，终止测试")
            return False
        
        tests = [
            self.test_system_stats,
            self.test_get_users,
            self.test_create_user,
            self.test_update_user,
            self.test_user_quota,
            self.test_ai_models,
            self.test_operation_logs,
            self.test_system_settings
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        # 清理
        self.cleanup_test_user()
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("⚠️  部分测试失败")
            return False

if __name__ == "__main__":
    tester = AdminAPITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
