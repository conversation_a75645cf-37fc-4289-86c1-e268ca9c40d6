"""
完整版向量化和搜索服务
使用Redis作为向量数据库，支持文档向量化和语义搜索
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import redis
import json
import hashlib
import asyncio
import aiohttp
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

class VectorServiceError(Exception):
    """向量服务异常"""
    pass

class EmbeddingService:
    """嵌入向量服务"""
    
    def __init__(self, api_key: str = "sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm"):
        self.api_key = api_key
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1"
        )
        self.model = "BAAI/bge-m3"  # 硅基流动支持的嵌入模型
    
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本的嵌入向量"""
        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=texts
            )
            
            embeddings = []
            for data in response.data:
                embeddings.append(data.embedding)
            
            return embeddings
            
        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            # 如果API失败，返回随机向量作为fallback
            return [[0.0] * 1024 for _ in texts]  # bge-large-zh-v1.5的维度是1024
    
    async def get_embedding(self, text: str) -> List[float]:
        """获取单个文本的嵌入向量"""
        embeddings = await self.get_embeddings([text])
        return embeddings[0] if embeddings else [0.0] * 1024

class VectorService:
    """完整版向量化和搜索服务"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client = None
        self.embedding_service = EmbeddingService()
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化Redis连接
            self.redis_client = redis.from_url(self.redis_url, decode_responses=False)
            self.redis_client.ping()
            
            logger.info("完整版向量服务初始化成功")
            
        except Exception as e:
            logger.error(f"向量服务初始化失败: {e}")
            logger.warning("向量服务将在模拟模式下运行")
            self.redis_client = None
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0
    
    def _get_kb_key(self, kb_id: int) -> str:
        """获取知识库键名"""
        return f"kb_{kb_id}_vectors"
    
    def _get_chunk_key(self, kb_id: int, document_id: int, chunk_id: int) -> str:
        """获取分块键名"""
        return f"kb_{kb_id}_doc_{document_id}_chunk_{chunk_id}"
    
    async def create_knowledge_base_index(self, kb_id: int) -> bool:
        """为知识库创建向量索引"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                # 创建知识库元数据
                metadata = {
                    "created_at": str(asyncio.get_event_loop().time()),
                    "kb_id": kb_id,
                    "vector_dim": 1024
                }
                # 使用兼容的hset语法
                for key, value in metadata.items():
                    self.redis_client.hset(f"{kb_key}_meta", key, value)
            
            logger.info(f"成功创建知识库 {kb_id} 的向量索引")
            return True
            
        except Exception as e:
            logger.error(f"创建知识库索引失败 {kb_id}: {e}")
            return False
    
    async def delete_knowledge_base_index(self, kb_id: int) -> bool:
        """删除知识库向量索引"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 删除所有相关的键
                pattern = f"{kb_key}*"
                keys = []
                for key in self.redis_client.scan_iter(match=pattern):
                    keys.append(key)
                
                if keys:
                    self.redis_client.delete(*keys)
            
            logger.info(f"成功删除知识库 {kb_id} 的向量索引")
            return True
            
        except Exception as e:
            logger.error(f"删除知识库索引失败 {kb_id}: {e}")
            return False
    
    async def add_document_chunks(
        self, 
        kb_id: int, 
        document_id: int, 
        chunks: List[Dict[str, Any]]
    ) -> bool:
        """添加文档分块到向量索引"""
        try:
            if not chunks:
                return True
            
            # 确保知识库索引存在
            await self.create_knowledge_base_index(kb_id)
            
            # 提取所有文本内容
            texts = [chunk.get('content', '') for chunk in chunks]
            
            # 获取嵌入向量
            embeddings = await self.embedding_service.get_embeddings(texts)
            
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 存储每个分块的向量和元数据
                for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                    chunk_key = self._get_chunk_key(kb_id, document_id, i)
                    
                    chunk_data = {
                        "document_id": document_id,
                        "chunk_id": i,
                        "content": chunk.get('content', ''),
                        "metadata": json.dumps(chunk.get('metadata', {})),
                        "embedding": json.dumps(embedding)
                    }
                    
                    # 存储分块数据（使用兼容的hset语法）
                    for key, value in chunk_data.items():
                        self.redis_client.hset(chunk_key, key, value)
                    
                    # 将分块键添加到知识库索引
                    self.redis_client.sadd(kb_key, chunk_key)
            
            logger.info(f"成功添加文档 {document_id} 的 {len(chunks)} 个分块到知识库 {kb_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加文档分块失败: {e}")
            return False
    
    async def remove_document_chunks(self, kb_id: int, document_id: int) -> bool:
        """从向量索引中移除文档分块"""
        try:
            if self.redis_client:
                kb_key = self._get_kb_key(kb_id)
                
                # 查找所有属于该文档的分块
                chunk_keys = self.redis_client.smembers(kb_key)
                
                keys_to_remove = []
                for chunk_key in chunk_keys:
                    chunk_data = self.redis_client.hgetall(chunk_key)
                    if chunk_data and int(chunk_data.get(b'document_id', 0)) == document_id:
                        keys_to_remove.append(chunk_key)
                
                # 删除分块数据和索引
                if keys_to_remove:
                    self.redis_client.delete(*keys_to_remove)
                    self.redis_client.srem(kb_key, *keys_to_remove)
            
            logger.info(f"成功移除文档 {document_id} 的分块从知识库 {kb_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除文档分块失败: {e}")
            return False
    
    async def search_similar_chunks(
        self, 
        kb_ids: List[int], 
        query: str, 
        top_k: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """在指定知识库中搜索相似的文档分块"""
        try:
            # 获取查询的嵌入向量
            query_embedding = await self.embedding_service.get_embedding(query)
            
            all_results = []
            
            for kb_id in kb_ids:
                if self.redis_client:
                    kb_key = self._get_kb_key(kb_id)
                    chunk_keys = self.redis_client.smembers(kb_key)
                    
                    for chunk_key in chunk_keys:
                        chunk_data = self.redis_client.hgetall(chunk_key)
                        if not chunk_data:
                            continue
                        
                        try:
                            # 解析嵌入向量
                            embedding = json.loads(chunk_data[b'embedding'].decode('utf-8'))
                            
                            # 计算相似度
                            similarity = self._cosine_similarity(query_embedding, embedding)
                            
                            if similarity >= similarity_threshold:
                                all_results.append({
                                    "kb_id": kb_id,
                                    "document_id": int(chunk_data[b'document_id']),
                                    "chunk_id": int(chunk_data[b'chunk_id']),
                                    "content": chunk_data[b'content'].decode('utf-8'),
                                    "metadata": json.loads(chunk_data[b'metadata'].decode('utf-8')),
                                    "similarity": similarity
                                })
                        except (json.JSONDecodeError, KeyError, ValueError) as e:
                            logger.warning(f"解析分块数据失败: {e}")
                            continue
            
            # 按相似度排序并返回top_k结果
            all_results.sort(key=lambda x: x['similarity'], reverse=True)
            return all_results[:top_k]
            
        except Exception as e:
            logger.error(f"搜索相似分块失败: {e}")
            return []
    
    async def get_knowledge_context(
        self,
        kb_ids: List[int],
        query: str,
        max_chunks: int = 3,
        similarity_threshold: float = 0.7
    ) -> str:
        """获取知识库上下文用于AI聊天"""
        try:
            similar_chunks = await self.search_similar_chunks(
                kb_ids=kb_ids,
                query=query,
                top_k=max_chunks,
                similarity_threshold=similarity_threshold
            )
            
            if not similar_chunks:
                return ""
            
            context_parts = []
            for chunk in similar_chunks:
                context_parts.append(
                    f"[相关度: {chunk['similarity']:.2f}] {chunk['content']}"
                )
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"获取知识上下文失败: {e}")
            return ""

# 全局向量服务实例
vector_service = VectorService()

def get_vector_service() -> VectorService:
    """获取向量服务实例"""
    return vector_service
