"""
测试所有API端点
"""
import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.admin_token = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, method: str, endpoint: str, data: Dict[Any, Any] = None, 
                          headers: Dict[str, str] = None, expected_status: int = 200) -> bool:
        """测试单个API端点"""
        url = f"{BASE_URL}{endpoint}"
        
        try:
            if headers is None:
                headers = {}
            
            if method.upper() == "GET":
                async with self.session.get(url, headers=headers) as response:
                    status = response.status
                    text = await response.text()
            elif method.upper() == "POST":
                headers["Content-Type"] = "application/json"
                async with self.session.post(url, json=data, headers=headers) as response:
                    status = response.status
                    text = await response.text()
            elif method.upper() == "PUT":
                headers["Content-Type"] = "application/json"
                async with self.session.put(url, json=data, headers=headers) as response:
                    status = response.status
                    text = await response.text()
            elif method.upper() == "DELETE":
                async with self.session.delete(url, headers=headers) as response:
                    status = response.status
                    text = await response.text()
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                return False
            
            if status == expected_status:
                print(f"✅ {method.upper()} {endpoint} - 状态码: {status}")
                return True
            else:
                print(f"❌ {method.upper()} {endpoint} - 期望状态码: {expected_status}, 实际: {status}")
                if status >= 400:
                    print(f"   错误信息: {text}")
                return False
                
        except Exception as e:
            print(f"❌ {method.upper()} {endpoint} - 连接错误: {e}")
            return False
    
    async def login(self, username: str, password: str) -> str:
        """登录并获取token"""
        login_data = {
            "username": username,
            "password": password
        }

        url = f"{BASE_URL}/api/auth/login"
        try:
            async with self.session.post(url, json=login_data) as response:
                if response.status == 200:
                    result = await response.json()
                    token = result.get("access_token")
                    print(f"✅ 登录成功: {username}")
                    return token
                else:
                    text = await response.text()
                    print(f"❌ 登录失败: {username} - {text}")
                    return None
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return None
    
    async def test_all_apis(self):
        """测试所有API"""
        print("🚀 开始测试所有API端点...\n")
        
        # 1. 测试健康检查
        print("📋 测试基础端点:")
        await self.test_endpoint("GET", "/")
        await self.test_endpoint("GET", "/health")
        
        # 2. 测试认证相关API
        print("\n🔐 测试认证API:")
        
        # 登录测试用户
        self.auth_token = await self.login("testuser", "test123")
        if self.auth_token:
            auth_headers = {"Authorization": f"Bearer {self.auth_token}"}
        else:
            auth_headers = {}
        
        # 登录管理员
        self.admin_token = await self.login("admin", "admin123")
        if self.admin_token:
            admin_headers = {"Authorization": f"Bearer {self.admin_token}"}
        else:
            admin_headers = {}
        
        # 测试用户信息
        await self.test_endpoint("GET", "/api/auth/me", headers=auth_headers)
        
        # 3. 测试用户管理API
        print("\n👥 测试用户管理API:")
        await self.test_endpoint("GET", "/api/users/", headers=admin_headers)
        await self.test_endpoint("GET", "/api/users/profile", headers=auth_headers)
        
        # 4. 测试AI模型API
        print("\n🤖 测试AI模型API:")
        await self.test_endpoint("GET", "/api/ai/models", headers=auth_headers)
        await self.test_endpoint("GET", "/api/ai/providers", headers=auth_headers)
        
        # 5. 测试知识库API
        print("\n📚 测试知识库API:")
        await self.test_endpoint("GET", "/api/knowledge-bases/", headers=auth_headers)
        
        # 创建知识库测试
        kb_data = {
            "name": "测试知识库",
            "description": "API测试用知识库"
        }
        await self.test_endpoint("POST", "/api/knowledge-bases/", data=kb_data, headers=auth_headers, expected_status=201)
        
        # 6. 测试聊天API
        print("\n💬 测试聊天API:")
        await self.test_endpoint("GET", "/api/chat/sessions", headers=auth_headers)
        
        # 创建聊天会话
        session_data = {
            "title": "测试聊天会话"
        }
        await self.test_endpoint("POST", "/api/chat/sessions", data=session_data, headers=auth_headers, expected_status=201)
        
        # 7. 测试系统设置API
        print("\n⚙️ 测试系统设置API:")
        await self.test_endpoint("GET", "/api/admin/settings", headers=admin_headers)
        
        # 8. 测试文档上传API (模拟)
        print("\n📄 测试文档API:")
        await self.test_endpoint("GET", "/api/documents/", headers=auth_headers)
        
        print("\n🎉 API测试完成！")

async def main():
    """主函数"""
    print("正在启动API测试...")
    print(f"目标服务器: {BASE_URL}")
    print("请确保后端服务正在运行 (uvicorn app.main:app --reload)\n")
    
    async with APITester() as tester:
        await tester.test_all_apis()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
