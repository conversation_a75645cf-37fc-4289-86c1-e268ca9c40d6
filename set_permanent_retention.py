#!/usr/bin/env python3
"""
设置对话历史为永久保留
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def set_permanent_retention():
    """设置对话历史为永久保留"""
    print("🔧 设置对话历史为永久保留...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 设置对话历史保留天数为0（永久）
        print("\n🔧 设置对话历史保留天数为0（永久）...")
        update_data = {
            "value": "0"
        }
        response = requests.put(f"{BASE_URL}/admin/settings/chat_retention_days", json=update_data, headers=headers)
        if response.status_code == 200:
            print("✅ 设置成功")
            
            # 验证设置
            response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
            if response.status_code == 200:
                settings = response.json()
                chat_retention = next((s for s in settings if s['key'] == 'chat_retention_days'), None)
                if chat_retention:
                    print(f"✅ 验证成功：对话历史保留天数 = {chat_retention['value']}")
                    if chat_retention['value'] == '0':
                        print("✅ 已设置为永久保留")
                    else:
                        print(f"⚠️ 当前设置为 {chat_retention['value']} 天")
        else:
            print(f"❌ 设置失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    set_permanent_retention()
