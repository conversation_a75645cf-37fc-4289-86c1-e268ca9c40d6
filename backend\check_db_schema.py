#!/usr/bin/env python3
"""
检查数据库表结构
"""
import asyncio
import psycopg2
from app.config import settings

async def check_table_schema():
    """检查documents表的结构"""
    try:
        # 连接数据库
        conn = psycopg2.connect(
            host=settings.database_host,
            port=settings.database_port,
            database=settings.database_name,
            user=settings.database_user,
            password=settings.database_password
        )
        
        cursor = conn.cursor()
        
        # 查询documents表的结构
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'documents' 
            ORDER BY ordinal_position;
        """)
        
        print("Documents表结构:")
        print("-" * 60)
        print(f"{'字段名':<20} {'类型':<20} {'最大长度':<10} {'可空':<10}")
        print("-" * 60)
        
        for row in cursor.fetchall():
            column_name, data_type, max_length, is_nullable = row
            max_length_str = str(max_length) if max_length else "无限制"
            print(f"{column_name:<20} {data_type:<20} {max_length_str:<10} {is_nullable:<10}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"检查数据库结构失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_table_schema())
