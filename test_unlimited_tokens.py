#!/usr/bin/env python3
"""
测试无限制tokens设置
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_unlimited_tokens():
    """测试无限制tokens设置"""
    print("🧪 测试无限制tokens设置...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 1. 获取AI模型列表
        print("\n📋 获取AI模型列表...")
        response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ 获取模型列表成功: {len(models)} 个模型")
            
            if models:
                test_model = models[0]
                print(f"测试模型: {test_model['display_name']} (ID: {test_model['id']})")
                print(f"当前max_tokens: {test_model['max_tokens']}")
                
                # 2. 测试设置为无限制（null）
                print(f"\n🔧 测试设置模型 {test_model['id']} 的max_tokens为无限制...")
                update_data = {
                    "max_tokens": None  # 设置为无限制
                }
                
                print(f"发送数据: {json.dumps(update_data)}")
                response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model['id']}", json=update_data, headers=headers)
                print(f"响应状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    print("✅ 设置无限制成功")
                    
                    # 验证设置
                    print("🔍 验证设置...")
                    response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
                    if response.status_code == 200:
                        updated_models = response.json()
                        updated_model = next((m for m in updated_models if m['id'] == test_model['id']), None)
                        if updated_model:
                            print(f"✅ 验证成功：max_tokens = {updated_model['max_tokens']}")
                            if updated_model['max_tokens'] is None:
                                print("🎉 无限制设置成功！")
                            else:
                                print(f"❌ 设置失败，当前值: {updated_model['max_tokens']}")
                        else:
                            print("❌ 找不到更新后的模型")
                    else:
                        print(f"❌ 验证失败: {response.status_code}")
                else:
                    print(f"❌ 设置无限制失败: {response.status_code} - {response.text}")
                
                # 3. 测试设置为自定义值
                print(f"\n🔧 测试设置模型 {test_model['id']} 的max_tokens为自定义值...")
                update_data = {
                    "max_tokens": 8000  # 设置为自定义值
                }
                
                print(f"发送数据: {json.dumps(update_data)}")
                response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model['id']}", json=update_data, headers=headers)
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 设置自定义值成功")
                    
                    # 验证设置
                    print("🔍 验证设置...")
                    response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
                    if response.status_code == 200:
                        updated_models = response.json()
                        updated_model = next((m for m in updated_models if m['id'] == test_model['id']), None)
                        if updated_model:
                            print(f"✅ 验证成功：max_tokens = {updated_model['max_tokens']}")
                            if updated_model['max_tokens'] == 8000:
                                print("🎉 自定义值设置成功！")
                            else:
                                print(f"❌ 设置失败，当前值: {updated_model['max_tokens']}")
                        else:
                            print("❌ 找不到更新后的模型")
                    else:
                        print(f"❌ 验证失败: {response.status_code}")
                else:
                    print(f"❌ 设置自定义值失败: {response.status_code} - {response.text}")
                    
            else:
                print("❌ 没有找到模型")
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_unlimited_tokens()
