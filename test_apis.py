#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ 健康检查: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_admin_login():
    """测试管理员登录"""
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 管理员登录成功")
            return data.get("access_token")
        else:
            print(f"❌ 管理员登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 管理员登录异常: {e}")
        return None

def test_ai_models_api(token):
    """测试AI模型API"""
    headers = {"Authorization": f"Bearer {token}"}

    try:
        # 测试获取AI提供商
        response = requests.get(f"{BASE_URL}/admin/ai-providers", headers=headers)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ AI提供商获取成功: {len(providers)} 个提供商")

            # 测试创建供应商
            import time
            timestamp = int(time.time())
            test_provider = {
                "name": f"test_provider_{timestamp}",
                "display_name": f"测试供应商_{timestamp}",
                "base_url": "https://api.test.com",
                "description": "测试用供应商",
                "is_active": True
            }
            response = requests.post(f"{BASE_URL}/admin/ai-providers",
                                   json=test_provider, headers=headers)
            if response.status_code == 200:
                created_provider = response.json()
                print(f"✅ 供应商创建成功: {created_provider['display_name']}")

                # 测试删除供应商
                response = requests.delete(f"{BASE_URL}/admin/ai-providers/{created_provider['id']}",
                                         headers=headers)
                if response.status_code == 200:
                    print("✅ 供应商删除成功")
                else:
                    print(f"❌ 供应商删除失败: {response.status_code}")
            else:
                print(f"❌ 供应商创建失败: {response.status_code}")
        else:
            print(f"❌ AI提供商获取失败: {response.status_code}")

        # 测试获取AI模型
        response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ AI模型获取成功: {len(models)} 个模型")

            # 测试更新模型（如果有模型的话）
            if models:
                model = models[0]
                update_data = {
                    "max_tokens": 8000,
                    "cost_per_1k_tokens": 0.01
                }
                response = requests.put(f"{BASE_URL}/admin/ai-models/{model['id']}",
                                      json=update_data, headers=headers)
                if response.status_code == 200:
                    print("✅ AI模型更新成功")
                else:
                    print(f"❌ AI模型更新失败: {response.status_code}")
        else:
            print(f"❌ AI模型获取失败: {response.status_code}")

    except Exception as e:
        print(f"❌ AI模型API测试异常: {e}")

def test_system_settings_api(token):
    """测试系统设置API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取系统设置
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        if response.status_code == 200:
            settings = response.json()
            print(f"✅ 系统设置获取成功: {len(settings)} 个设置")
        else:
            print(f"❌ 系统设置获取失败: {response.status_code}")
            
        # 测试更新系统设置
        test_setting = {
            "value": "test_value_123",
            "description": "测试设置项"
        }
        response = requests.put(f"{BASE_URL}/admin/settings/test_setting", 
                              json=test_setting, headers=headers)
        if response.status_code == 200:
            print("✅ 系统设置更新成功")
        else:
            print(f"❌ 系统设置更新失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 系统设置API测试异常: {e}")

def test_logs_api(token):
    """测试日志API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取操作日志
        response = requests.get(f"{BASE_URL}/admin/logs?limit=5", headers=headers)
        if response.status_code == 200:
            logs = response.json()
            print(f"✅ 操作日志获取成功: {len(logs)} 条日志")
        else:
            print(f"❌ 操作日志获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 日志API测试异常: {e}")

def test_comprehensive_features(token):
    """综合功能测试"""
    headers = {"Authorization": f"Bearer {token}"}

    print("\n🔧 综合功能测试...")

    try:
        # 1. 测试系统设置的完整流程
        print("  测试系统设置...")

        # 获取当前设置
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        if response.status_code == 200:
            settings = response.json()
            print(f"  ✅ 获取系统设置成功: {len(settings)} 个设置")

            # 测试更新设置
            test_settings = [
                {"key": "max_file_size_mb", "value": "100", "description": "最大文件上传大小(MB)"},
                {"key": "allow_user_registration", "value": "true", "description": "是否允许用户注册"},
                {"key": "default_storage_quota_gb", "value": "20", "description": "用户默认存储配额(GB)"}
            ]

            for setting in test_settings:
                response = requests.put(f"{BASE_URL}/admin/settings/{setting['key']}",
                                      json={"value": setting["value"], "description": setting["description"]},
                                      headers=headers)
                if response.status_code == 200:
                    print(f"  ✅ 更新设置成功: {setting['key']}")
                else:
                    print(f"  ❌ 更新设置失败: {setting['key']} - {response.status_code}")

        # 2. 测试AI模型管理的完整流程
        print("  测试AI模型管理...")

        # 获取所有供应商和模型
        providers_response = requests.get(f"{BASE_URL}/admin/ai-providers", headers=headers)
        models_response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)

        if providers_response.status_code == 200 and models_response.status_code == 200:
            providers = providers_response.json()
            models = models_response.json()
            print(f"  ✅ 获取供应商和模型成功: {len(providers)} 个供应商, {len(models)} 个模型")

            # 测试模型更新（如果有模型）
            if models:
                model = models[0]
                update_data = {
                    "max_tokens": 16000,
                    "cost_per_1k_tokens": 0.02,
                    "is_active": True
                }
                response = requests.put(f"{BASE_URL}/admin/ai-models/{model['id']}",
                                      json=update_data, headers=headers)
                if response.status_code == 200:
                    print(f"  ✅ 模型更新成功: {model['display_name']}")
                else:
                    print(f"  ❌ 模型更新失败: {response.status_code}")

        print("  ✅ 综合功能测试完成")

    except Exception as e:
        print(f"  ❌ 综合功能测试异常: {e}")

def main():
    print("🚀 开始API测试...")

    # 1. 健康检查
    if not test_health():
        return

    # 2. 管理员登录
    token = test_admin_login()
    if not token:
        return

    # 3. 测试各个API
    print("\n📊 测试AI模型管理API...")
    test_ai_models_api(token)

    print("\n⚙️ 测试系统设置API...")
    test_system_settings_api(token)

    print("\n📝 测试日志API...")
    test_logs_api(token)

    # 4. 综合功能测试
    test_comprehensive_features(token)

    print("\n✅ 所有API测试完成!")

if __name__ == "__main__":
    main()
