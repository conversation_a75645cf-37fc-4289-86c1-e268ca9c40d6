"""
初始化AI提供商数据
"""
import asyncio
from sqlmodel import Session, select
from app.core.database import engine
from app.models.ai import AIProvider
from app.config import settings


async def init_ai_providers():
    """初始化AI提供商数据"""
    
    # AI提供商数据
    providers_data = [
        {
            "provider_name": "OpenAI",
            "model_name": "gpt-4",
            "is_active": True,
            "system_api_key": settings.openai_api_key,
            "allow_system_key_use": bool(settings.openai_api_key)
        },
        {
            "provider_name": "OpenAI",
            "model_name": "gpt-3.5-turbo",
            "is_active": True,
            "system_api_key": settings.openai_api_key,
            "allow_system_key_use": bool(settings.openai_api_key)
        },
        {
            "provider_name": "Anthropic",
            "model_name": "claude-3-opus",
            "is_active": True,
            "system_api_key": settings.anthropic_api_key,
            "allow_system_key_use": bool(settings.anthropic_api_key)
        },
        {
            "provider_name": "Anthropic",
            "model_name": "claude-3-sonnet",
            "is_active": True,
            "system_api_key": settings.anthropic_api_key,
            "allow_system_key_use": bool(settings.anthropic_api_key)
        },
        {
            "provider_name": "Google",
            "model_name": "gemini-pro",
            "is_active": True,
            "system_api_key": settings.google_api_key,
            "allow_system_key_use": bool(settings.google_api_key)
        },
        {
            "provider_name": "硅基流动",
            "model_name": "qwen-plus",
            "is_active": True,
            "system_api_key": settings.siliconflow_api_key,
            "allow_system_key_use": True
        },
        {
            "provider_name": "硅基流动",
            "model_name": "qwen-turbo",
            "is_active": True,
            "system_api_key": settings.siliconflow_api_key,
            "allow_system_key_use": True
        },
        {
            "provider_name": "DeepSeek",
            "model_name": "deepseek-chat",
            "is_active": True,
            "system_api_key": settings.deepseek_api_key,
            "allow_system_key_use": bool(settings.deepseek_api_key)
        },
        {
            "provider_name": "DeepSeek",
            "model_name": "deepseek-coder",
            "is_active": True,
            "system_api_key": settings.deepseek_api_key,
            "allow_system_key_use": bool(settings.deepseek_api_key)
        }
    ]
    
    with Session(engine) as session:
        # 检查是否已经有数据
        existing_providers = session.exec(select(AIProvider)).all()
        
        if existing_providers:
            print("AI提供商数据已存在，正在更新...")
            # 更新现有数据
            for provider_data in providers_data:
                statement = select(AIProvider).where(
                    AIProvider.provider_name == provider_data["provider_name"],
                    AIProvider.model_name == provider_data["model_name"]
                )
                existing_provider = session.exec(statement).first()
                
                if existing_provider:
                    # 更新现有记录
                    for key, value in provider_data.items():
                        setattr(existing_provider, key, value)
                else:
                    # 创建新记录
                    new_provider = AIProvider(**provider_data)
                    session.add(new_provider)
        else:
            print("正在创建AI提供商数据...")
            # 创建新数据
            for provider_data in providers_data:
                provider = AIProvider(**provider_data)
                session.add(provider)
        
        session.commit()
        print("AI提供商数据初始化完成！")
        
        # 显示当前的提供商列表
        all_providers = session.exec(select(AIProvider)).all()
        print(f"\n当前共有 {len(all_providers)} 个AI模型:")
        for provider in all_providers:
            status = "✓" if provider.is_active else "✗"
            system_key_status = "有系统密钥" if provider.system_api_key else "无系统密钥"
            print(f"  {status} {provider.provider_name} - {provider.model_name} ({system_key_status})")


if __name__ == "__main__":
    asyncio.run(init_ai_providers())
