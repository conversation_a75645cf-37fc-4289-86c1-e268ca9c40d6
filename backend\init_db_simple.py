"""
简单的数据库初始化脚本
"""
import sys
import os
from datetime import datetime
from sqlmodel import SQLModel, Session, create_engine, select
from passlib.context import CryptContext

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入所有模型
from app.models.user import User, UserQuota
from app.models.ai import AIProvider, UserAPIKey
from app.models.knowledge_base import KnowledgeBase, Document
from app.models.system import SystemSetting, OperationLog

# 数据库配置
DATABASE_URL = "postgresql+psycopg2://postgres:111222@localhost:5432/aiknowledgebase"

# 创建引擎
engine = create_engine(DATABASE_URL, echo=True)

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def init_database():
    """初始化数据库"""
    print("正在创建数据库表...")
    
    # 创建所有表
    SQLModel.metadata.create_all(engine)
    print("✓ 数据库表创建完成！")
    
    with Session(engine) as session:
        # 检查是否已有数据
        existing_users = session.exec(select(User)).all()
        if existing_users:
            print("数据库已有用户数据，跳过初始化")
            return
        
        print("正在添加测试数据...")
        
        # 1. 创建系统设置
        system_settings = [
            SystemSetting(key="allow_registration", value="true", description="是否允许用户注册"),
            SystemSetting(key="max_file_size", value="50", description="最大文件上传大小(MB)"),
            SystemSetting(key="default_storage_quota", value="1024", description="默认存储配额(MB)"),
        ]
        for setting in system_settings:
            session.add(setting)
        
        # 2. 创建AI提供商
        ai_providers = [
            AIProvider(
                provider_name="OpenAI",
                model_name="gpt-4",
                is_active=True,
                system_api_key="",
                allow_system_key_use=False
            ),
            AIProvider(
                provider_name="OpenAI", 
                model_name="gpt-3.5-turbo",
                is_active=True,
                system_api_key="",
                allow_system_key_use=False
            ),
            AIProvider(
                provider_name="硅基流动",
                model_name="qwen-plus",
                is_active=True,
                system_api_key="sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm",
                allow_system_key_use=True
            ),
            AIProvider(
                provider_name="硅基流动",
                model_name="qwen-turbo", 
                is_active=True,
                system_api_key="sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm",
                allow_system_key_use=True
            ),
            AIProvider(
                provider_name="DeepSeek",
                model_name="deepseek-chat",
                is_active=False,
                system_api_key="",
                allow_system_key_use=False
            ),
        ]
        for provider in ai_providers:
            session.add(provider)
        
        session.commit()  # 提交AI提供商数据
        
        # 3. 创建测试用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=hash_password("admin123"),
            display_name="系统管理员",
            is_admin=True,
            status="active",
            last_login_at=datetime.utcnow()
        )
        session.add(admin_user)
        
        test_user = User(
            username="testuser",
            email="<EMAIL>", 
            password_hash=hash_password("test123"),
            display_name="测试用户",
            is_admin=False,
            status="active",
            last_login_at=datetime.utcnow()
        )
        session.add(test_user)
        
        session.commit()  # 提交用户数据
        
        # 4. 创建用户配额
        admin_quota = UserQuota(
            user_id=admin_user.id,
            max_kbs=50,
            max_docs_per_kb=1000,
            max_storage_mb=10240
        )
        session.add(admin_quota)
        
        user_quota = UserQuota(
            user_id=test_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        
        # 5. 创建测试知识库
        kb1 = KnowledgeBase(
            owner_id=test_user.id,
            name="技术文档库",
            description="存储技术相关文档的知识库"
        )
        session.add(kb1)
        
        session.commit()
        
        print("✓ 测试数据添加完成！")
        print(f"✓ 管理员账号: admin / admin123")
        print(f"✓ 测试用户账号: testuser / test123")
        print(f"✓ 创建了 {len(ai_providers)} 个AI模型")
        print(f"✓ 创建了 1 个知识库")

if __name__ == "__main__":
    try:
        init_database()
        print("\n🎉 数据库初始化成功！")
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
