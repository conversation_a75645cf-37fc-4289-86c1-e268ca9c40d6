<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端图表解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-result {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error {
            background: #f8e8e8;
            border: 1px solid #e6c3c3;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>前端图表解析测试</h1>
    
    <div class="test-section">
        <h2>测试消息内容</h2>
        <div class="test-input" id="message-content">这里是一个ECharts图表示例：

```javascript
option = {
  title: { text: '销售数据统计' },
  xAxis: { data: ['1月', '2月', '3月', '4月'] },
  yAxis: {},
  series: [{
    name: '销售额',
    type: 'bar',
    data: [120, 200, 150, 80]
  }]
}
```

这个图表展示了月度销售数据。</div>
        <button onclick="testExtraction()">测试提取</button>
        <div class="console-output" id="console-output"></div>
        <div class="test-result" id="extraction-result"></div>
    </div>

    <script>
        // 重定向console.log到页面
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.innerHTML += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // 复制前端的图表解析函数
        function isChartCode(code) {
            const chartKeywords = [
                // Chart.js 格式
                'type:', 'labels:', 'datasets:', 'data:',
                'pie', 'bar', 'line', 'chart', 'doughnut', 'radar', 'polararea',
                '图表', '饼图', '柱状图', '折线图', '环形图', '雷达图',
                'backgroundColor', 'borderColor', 'chartjs',

                // ECharts 格式
                'echarts', 'option', 'xAxis', 'yAxis', 'series', 'legend',
                'tooltip', 'grid', 'dataZoom', 'brush', 'geo', 'parallel',
                'sankey', 'funnel', 'gauge', 'pictorialBar', 'themeRiver',
                'sunburst', 'tree', 'treemap', 'graph', 'boxplot', 'candlestick',
                'heatmap', 'map', 'lines', 'effectScatter', 'scatter3D',

                // Python matplotlib 格式
                'matplotlib', 'pyplot', 'plt.', 'import matplotlib',
                'plt.figure', 'plt.plot', 'plt.bar', 'plt.pie', 'plt.scatter',
                'plt.show', 'plt.xlabel', 'plt.ylabel', 'plt.title',
                'figsize', 'labels =', 'data =', 'values =',

                // 其他图表库
                'seaborn', 'plotly', 'sns.', 'px.', 'go.',
                'altair', 'bokeh'
            ];

            const lowerCode = code.toLowerCase();
            return chartKeywords.some(keyword => lowerCode.includes(keyword.toLowerCase()));
        }

        function extractChartBlocks(text) {
            const chartBlocks = [];
            
            console.log('开始提取图表代码块，输入文本:', text);

            // 重置正则表达式的lastIndex
            const resetRegex = (regex) => {
                regex.lastIndex = 0;
                return regex;
            };

            // 匹配各种代码块格式
            const codeBlockPatterns = [
                // 带语言标识的代码块
                resetRegex(/```(?:json|javascript|js|chart|图表|python|py|matplotlib|echarts|option)\s*\n([\s\S]*?)\n```/gi),
                // 无语言标识的代码块
                resetRegex(/```\s*\n([\s\S]*?)\n```/gi),
                // 简单的代码块（可能没有换行）
                resetRegex(/```([\s\S]*?)```/gi)
            ];

            for (const pattern of codeBlockPatterns) {
                let match;
                while ((match = pattern.exec(text)) !== null) {
                    const code = match[1].trim();
                    console.log('找到代码块:', code);

                    // 检查是否包含图表相关关键词
                    if (isChartCode(code)) {
                        console.log('确认为图表代码，添加到结果中');
                        chartBlocks.push(code);
                    } else {
                        console.log('不是图表代码，跳过');
                    }
                }
                // 重置正则表达式状态
                pattern.lastIndex = 0;
            }

            // 如果没有找到代码块，尝试直接解析整个文本中的对象
            if (chartBlocks.length === 0) {
                console.log('没有找到代码块，尝试直接解析对象');
                
                // 查找option = { ... } 模式
                const optionMatch = text.match(/option\s*=\s*({[\s\S]*?})/gi);
                if (optionMatch) {
                    for (const match of optionMatch) {
                        const objMatch = match.match(/{[\s\S]*}/);
                        if (objMatch) {
                            const code = objMatch[0];
                            console.log('找到option对象:', code);
                            if (isChartCode(code)) {
                                chartBlocks.push(code);
                            }
                        }
                    }
                }
                
                // 查找独立的JSON对象
                if (chartBlocks.length === 0) {
                    const jsonMatches = text.match(/{[\s\S]*?}/g);
                    if (jsonMatches) {
                        for (const jsonStr of jsonMatches) {
                            console.log('检查JSON对象:', jsonStr);
                            if (isChartCode(jsonStr)) {
                                console.log('确认为图表JSON，添加到结果中');
                                chartBlocks.push(jsonStr);
                            }
                        }
                    }
                }
            }

            console.log('最终提取的图表代码块:', chartBlocks);
            return chartBlocks;
        }

        function testExtraction() {
            // 清空控制台输出
            consoleOutput.innerHTML = '';
            
            const messageContent = document.getElementById('message-content').textContent;
            const resultDiv = document.getElementById('extraction-result');
            
            try {
                console.log('=== 开始测试图表提取 ===');
                const chartBlocks = extractChartBlocks(messageContent);
                
                if (chartBlocks.length > 0) {
                    resultDiv.className = 'test-result';
                    resultDiv.innerHTML = `
                        <h4>提取成功！</h4>
                        <p><strong>找到 ${chartBlocks.length} 个图表代码块：</strong></p>
                        ${chartBlocks.map((block, index) => `
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <strong>代码块 ${index + 1}:</strong>
                                <pre style="margin: 5px 0; font-size: 12px;">${block}</pre>
                            </div>
                        `).join('')}
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '<h4>提取失败</h4><p>没有找到图表代码块</p>';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<h4>提取错误</h4><p>${error.message}</p>`;
                console.error('提取错误:', error);
            }
        }
    </script>
</body>
</html>
