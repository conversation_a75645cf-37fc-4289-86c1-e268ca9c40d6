"""
简单的文档上传和向量化测试
"""
import requests
import tempfile
import os

BASE_URL = "http://localhost:8000"

def test_simple_upload():
    """测试简单的文档上传和向量化"""
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {"username": "testuser", "password": "test123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 创建知识库
    print("📚 正在创建知识库...")
    kb_data = {
        "name": "简单测试知识库",
        "description": "用于测试文档上传和向量化"
    }
    response = requests.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=headers)
    
    if response.status_code not in [200, 201]:
        print(f"❌ 创建知识库失败: {response.text}")
        return
    
    kb_id = response.json().get("id")
    print(f"✅ 创建知识库成功 - ID: {kb_id}")
    
    # 3. 创建测试文档
    print("📄 正在创建测试文档...")
    test_content = """人工智能基础知识

什么是人工智能？
人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

人工智能的主要技术：
1. 机器学习（Machine Learning）
   机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。

2. 深度学习（Deep Learning）
   深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。

3. 自然语言处理（NLP）
   自然语言处理使计算机能够理解、解释和生成人类语言。

4. 计算机视觉（Computer Vision）
   计算机视觉使机器能够识别和理解图像和视频中的内容。

人工智能的应用领域：
- 智能助手（如Siri、Alexa）
- 自动驾驶汽车
- 医疗诊断
- 金融风险评估
- 推荐系统
- 语言翻译

人工智能的发展历程：
1950年代：人工智能概念提出
1960年代：专家系统发展
1980年代：机器学习兴起
2000年代：大数据推动AI发展
2010年代：深度学习突破
2020年代：大语言模型时代

未来展望：
人工智能将继续快速发展，在更多领域发挥重要作用。同时，我们也需要关注AI的伦理问题，确保技术发展造福人类。"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        # 4. 上传文档
        print("📤 正在上传文档...")
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('ai_knowledge.txt', f, 'text/plain')}
            response = requests.post(
                f"{BASE_URL}/api/documents/upload/{kb_id}",
                files=files,
                headers=headers
            )
        
        if response.status_code not in [200, 201]:
            print(f"❌ 文档上传失败: {response.status_code} - {response.text}")
            return
        
        doc_data = response.json()
        doc_id = doc_data.get("id")
        status = doc_data.get("status")
        print(f"✅ 文档上传成功 - ID: {doc_id}, 状态: {status}")
        
        # 5. 检查文档状态，如果需要则手动触发向量化
        if status != "completed":
            print("🔄 文档状态不是completed，开始手动向量化...")
            response = requests.post(
                f"{BASE_URL}/api/documents/{doc_id}/vectorize",
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 文档向量化成功!")
                print(f"   状态: {result.get('status')}")
                if 'chunks_count' in result:
                    print(f"   分块数量: {result.get('chunks_count')}")
                print(f"   响应详情: {result}")
            else:
                print(f"❌ 文档向量化失败: {response.status_code}")
                print(f"   错误详情: {response.text}")
                return
        else:
            print("✅ 文档已自动完成向量化")
        
        # 6. 测试基于知识库的聊天
        print("\n💬 测试基于知识库的聊天...")
        
        # 创建聊天会话
        session_data = {"title": "知识库测试聊天"}
        response = requests.post(f"{BASE_URL}/api/chat/sessions", json=session_data, headers=headers)
        
        if response.status_code not in [200, 201]:
            print(f"❌ 创建聊天会话失败: {response.text}")
            return
        
        chat_session_id = response.json().get("id")
        print(f"✅ 创建聊天会话成功 - ID: {chat_session_id}")
        
        # 发送问题
        chat_data = {
            "message": "什么是机器学习？请根据知识库内容回答。",
            "model_id": "Qwen/Qwen2.5-7B-Instruct",
            "knowledge_base_ids": [kb_id]
        }
        
        print("🤖 发送问题: 什么是机器学习？")
        response = requests.post(
            f"{BASE_URL}/api/chat/sessions/{chat_session_id}/stream",
            json=chat_data,
            headers=headers,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ 开始接收AI响应:")
            content_received = ""
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8').strip()
                    if line_str.startswith('data: '):
                        try:
                            import json
                            data = json.loads(line_str[6:])
                            if data.get('type') == 'content':
                                content = data.get('content', '')
                                content_received += content
                                print(content, end='', flush=True)
                            elif data.get('type') == 'error':
                                print(f"\n⚠️ AI响应错误: {data.get('content')}")
                            elif data.get('type') == 'done':
                                break
                        except json.JSONDecodeError:
                            continue
            
            print(f"\n\n✅ AI响应完成，共接收 {len(content_received)} 个字符")
            
            if "机器学习" in content_received:
                print("✅ AI响应包含相关知识库内容")
            else:
                print("⚠️ AI响应可能未使用知识库内容")
        else:
            print(f"❌ 聊天请求失败: {response.status_code} - {response.text}")
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_simple_upload()
