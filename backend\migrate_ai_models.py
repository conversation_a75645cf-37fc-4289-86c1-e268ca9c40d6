#!/usr/bin/env python3
"""
AI模型数据库迁移脚本
将现有的AIProvider表结构迁移到新的AIProvider + AIModel结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import Session, select
from app.core.database import engine
from app.models.ai import AIProvider, AIModel, UserAPIKey
from app.config import settings

def migrate_ai_models():
    """迁移AI模型数据"""
    
    with Session(engine) as session:
        print("开始迁移AI模型数据...")
        
        # 1. 创建新的供应商数据
        providers_data = [
            {
                "name": "siliconflow",
                "display_name": "硅基流动",
                "base_url": "https://api.siliconflow.cn/v1",
                "description": "硅基流动AI平台，提供多种开源大模型服务"
            },
            {
                "name": "openai", 
                "display_name": "OpenAI",
                "base_url": "https://api.openai.com/v1",
                "description": "OpenAI官方API服务"
            },
            {
                "name": "deepseek",
                "display_name": "DeepSeek",
                "base_url": "https://api.deepseek.com/v1", 
                "description": "DeepSeek AI平台"
            },
            {
                "name": "anthropic",
                "display_name": "Anthropic",
                "base_url": "https://api.anthropic.com",
                "description": "Anthropic Claude系列模型"
            },
            {
                "name": "google",
                "display_name": "Google",
                "base_url": "https://generativelanguage.googleapis.com/v1",
                "description": "Google Gemini系列模型"
            }
        ]
        
        # 创建供应商
        provider_map = {}
        for provider_data in providers_data:
            # 检查是否已存在
            existing = session.exec(
                select(AIProvider).where(AIProvider.name == provider_data["name"])
            ).first()
            
            if not existing:
                provider = AIProvider(**provider_data)
                session.add(provider)
                session.commit()
                session.refresh(provider)
                provider_map[provider_data["name"]] = provider
                print(f"创建供应商: {provider_data['display_name']}")
            else:
                provider_map[provider_data["name"]] = existing
                print(f"供应商已存在: {provider_data['display_name']}")
        
        # 2. 创建模型数据
        models_data = [
            # 硅基流动模型
            {
                "provider": "siliconflow",
                "model_name": "Qwen/Qwen2.5-7B-Instruct",
                "display_name": "Qwen2.5-7B-Instruct",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0007
            },
            {
                "provider": "siliconflow", 
                "model_name": "Qwen/Qwen2.5-14B-Instruct",
                "display_name": "Qwen2.5-14B-Instruct",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0014
            },
            {
                "provider": "siliconflow",
                "model_name": "Qwen/Qwen2.5-32B-Instruct", 
                "display_name": "Qwen2.5-32B-Instruct",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0024
            },
            {
                "provider": "siliconflow",
                "model_name": "Qwen/Qwen2.5-72B-Instruct",
                "display_name": "Qwen2.5-72B-Instruct", 
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0056
            },
            {
                "provider": "siliconflow",
                "model_name": "deepseek-ai/DeepSeek-V2.5",
                "display_name": "DeepSeek-V2.5",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0014
            },
            {
                "provider": "siliconflow",
                "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
                "display_name": "Llama-3.1-8B-Instruct",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.0007
            },
            # OpenAI模型
            {
                "provider": "openai",
                "model_name": "gpt-4",
                "display_name": "GPT-4",
                "max_tokens": 8192,
                "cost_per_1k_tokens": 0.03
            },
            {
                "provider": "openai",
                "model_name": "gpt-4-turbo",
                "display_name": "GPT-4 Turbo",
                "max_tokens": 128000,
                "cost_per_1k_tokens": 0.01
            },
            {
                "provider": "openai",
                "model_name": "gpt-3.5-turbo",
                "display_name": "GPT-3.5 Turbo",
                "max_tokens": 4096,
                "cost_per_1k_tokens": 0.002
            },
            # DeepSeek模型
            {
                "provider": "deepseek",
                "model_name": "deepseek-chat",
                "display_name": "DeepSeek Chat",
                "max_tokens": 4096,
                "cost_per_1k_tokens": 0.0014
            },
            {
                "provider": "deepseek",
                "model_name": "deepseek-coder",
                "display_name": "DeepSeek Coder",
                "max_tokens": 4096,
                "cost_per_1k_tokens": 0.0014
            }
        ]
        
        # 创建模型
        for model_data in models_data:
            provider = provider_map[model_data["provider"]]
            
            # 检查是否已存在
            existing = session.exec(
                select(AIModel).where(
                    AIModel.provider_id == provider.id,
                    AIModel.model_name == model_data["model_name"]
                )
            ).first()
            
            if not existing:
                model = AIModel(
                    provider_id=provider.id,
                    model_name=model_data["model_name"],
                    display_name=model_data["display_name"],
                    is_active=True,
                    allow_system_key_use=True,
                    max_tokens=model_data["max_tokens"],
                    supports_streaming=True,
                    cost_per_1k_tokens=model_data["cost_per_1k_tokens"],
                    system_api_key=settings.siliconflow_api_key if model_data["provider"] == "siliconflow" else None
                )
                session.add(model)
                print(f"创建模型: {model_data['display_name']}")
            else:
                print(f"模型已存在: {model_data['display_name']}")
        
        session.commit()
        print("AI模型数据迁移完成!")

if __name__ == "__main__":
    migrate_ai_models()
