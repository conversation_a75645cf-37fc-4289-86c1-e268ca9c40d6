"""
测试数据库连接
"""
import asyncio
import asyncpg
import psycopg2
from app.config import settings

async def test_asyncpg():
    """测试asyncpg连接"""
    try:
        # 先连接到postgres数据库
        conn = await asyncpg.connect(
            host=settings.database_host,
            port=settings.database_port,
            user=settings.database_user,
            password=settings.database_password,
            database="postgres"
        )
        print("✓ 成功连接到PostgreSQL服务器")
        
        # 检查目标数据库是否存在
        result = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1",
            settings.database_name
        )
        
        if result:
            print(f"✓ 数据库 '{settings.database_name}' 已存在")
        else:
            print(f"✗ 数据库 '{settings.database_name}' 不存在，正在创建...")
            await conn.execute(f'CREATE DATABASE "{settings.database_name}"')
            print(f"✓ 数据库 '{settings.database_name}' 创建成功")
        
        await conn.close()
        
        # 连接到目标数据库
        target_conn = await asyncpg.connect(
            host=settings.database_host,
            port=settings.database_port,
            user=settings.database_user,
            password=settings.database_password,
            database=settings.database_name
        )
        print(f"✓ 成功连接到目标数据库 '{settings.database_name}'")
        await target_conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_psycopg2():
    """测试psycopg2连接"""
    try:
        conn = psycopg2.connect(
            host=settings.database_host,
            port=settings.database_port,
            user=settings.database_user,
            password=settings.database_password,
            database="postgres"
        )
        print("✓ psycopg2连接成功")
        conn.close()
        return True
    except Exception as e:
        print(f"✗ psycopg2连接失败: {e}")
        return False

async def main():
    print("正在测试数据库连接...")
    print(f"数据库配置:")
    print(f"  主机: {settings.database_host}")
    print(f"  端口: {settings.database_port}")
    print(f"  用户: {settings.database_user}")
    print(f"  数据库: {settings.database_name}")
    print()
    
    # 测试psycopg2
    print("测试 psycopg2 连接:")
    test_psycopg2()
    print()
    
    # 测试asyncpg
    print("测试 asyncpg 连接:")
    success = await test_asyncpg()
    
    if success:
        print("\n✓ 所有数据库连接测试通过！")
    else:
        print("\n✗ 数据库连接测试失败！")
        print("请检查:")
        print("1. PostgreSQL服务是否正在运行")
        print("2. 数据库配置是否正确")
        print("3. 用户权限是否足够")

if __name__ == "__main__":
    asyncio.run(main())
