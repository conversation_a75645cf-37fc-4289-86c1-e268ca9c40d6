<template>
  <div class="html-preview-container">
    <div v-if="htmlContent" class="html-wrapper">
      <div class="preview-header">
        <div class="header-left">
          <div class="traffic-lights">
            <div class="light red"></div>
            <div class="light yellow"></div>
            <div class="light green"></div>
          </div>
          <span class="preview-title">HTML 预览</span>
        </div>
        <div class="header-right">

          <button
            @click="openModal"
            class="action-btn"
            title="放大预览"
          >
            <el-icon><FullScreen /></el-icon>
          </button>
          <button
            @click="refreshPreview"
            class="action-btn"
            title="刷新"
          >
            <el-icon><Refresh /></el-icon>
          </button>
          <button
            @click="copyHtml"
            class="action-btn"
            title="复制HTML"
          >
            <el-icon><DocumentCopy /></el-icon>
          </button>
        </div>
      </div>
      <div :class="['iframe-container', { 'auto-height': props.height === 'auto' }]">
        <iframe
          ref="previewFrame"
          :srcdoc="sanitizedHtml"
          class="preview-iframe"
          @load="onIframeLoad"
        ></iframe>
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>
    <div v-else class="error-message">
      <el-icon><Warning /></el-icon>
      <span>无效的HTML内容</span>
    </div>

    <!-- 弹窗预览 -->
    <HtmlPreviewModal
      v-model="showModal"
      :html-content="htmlContent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  FullScreen,
  Refresh,
  DocumentCopy,
  Warning,
  Loading
} from '@element-plus/icons-vue'
import HtmlPreviewModal from './HtmlPreviewModal.vue'

interface Props {
  htmlContent: string
  height?: string
  allowScripts?: boolean
  minHeight?: string
  maxHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: 'auto',
  allowScripts: true,
  minHeight: '300px',
  maxHeight: '600px'
})

const previewFrame = ref<HTMLIFrameElement>()
const loading = ref(true)
const showModal = ref(false)


// 本地库文件路径
const getLocalLibraryUrls = () => {
  const baseUrl = window.location.origin
  return {
    chartJs: baseUrl + '/libs/chart.js',
    echarts: baseUrl + '/libs/echarts.js'
  }
}

// 清理和安全化HTML内容
const sanitizedHtml = computed(() => {
  if (!props.htmlContent) {
    return ''
  }

  let html = props.htmlContent.trim()

  // 如果不是完整的HTML文档，包装成完整文档并添加图表库
  if (!html.includes('<!DOCTYPE') && !html.includes('<html')) {
    const libs = getLocalLibraryUrls()
    html = createFullHtmlDocument(html, libs)
  } else {
    // 如果是完整HTML，确保包含图表库
    if (!html.includes('chart.js') && !html.includes('echarts')) {
      const libs = getLocalLibraryUrls()
      html = addLibrariesToExistingHtml(html, libs)
    }
  }

  // 如果不允许脚本，移除script标签
  if (!props.allowScripts) {
    const scriptStartTag = '<script'
    const scriptEndTag = '</' + 'script' + '>'
    let startIndex = html.indexOf(scriptStartTag)
    while (startIndex !== -1) {
      const endIndex = html.indexOf(scriptEndTag, startIndex)
      if (endIndex !== -1) {
        html = html.substring(0, startIndex) + html.substring(endIndex + scriptEndTag.length)
        startIndex = html.indexOf(scriptStartTag, startIndex)
      } else {
        break
      }
    }
  }

  return html
})

// 创建完整的HTML文档
function createFullHtmlDocument(content: string, libs: any): string {
  const parts = []
  parts.push('<!' + 'DOCTYPE html>')
  parts.push('<' + 'html lang="zh-CN">')
  parts.push('<' + 'head>')
  parts.push('    <' + 'meta charset="UTF-8">')
  parts.push('    <' + 'meta name="viewport" content="width=device-width, initial-scale=1.0">')
  parts.push('    <' + 'title>HTML预览</' + 'title>')
  parts.push('    <' + 'script src="' + libs.chartJs + '"></' + 'script>')
  parts.push('    <' + 'script src="' + libs.echarts + '"></' + 'script>')
  parts.push('    <' + 'style>')
  parts.push('        body {')
  parts.push('            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;')
  parts.push('            margin: 20px;')
  parts.push('            line-height: 1.6;')
  parts.push('            color: #333;')
  parts.push('        }')
  parts.push('        * {')
  parts.push('            box-sizing: border-box;')
  parts.push('        }')
  parts.push('        .chart-container {')
  parts.push('            width: 100%;')
  parts.push('            height: 400px;')
  parts.push('            margin: 20px 0;')
  parts.push('        }')
  parts.push('    </' + 'style>')
  parts.push('</' + 'head>')
  parts.push('<' + 'body>')
  parts.push('    ' + content)
  parts.push('</' + 'body>')
  parts.push('</' + 'html>')
  return parts.join('\n')
}

// 向现有HTML添加库
function addLibrariesToExistingHtml(html: string, libs: any): string {
  const scriptParts = []
  scriptParts.push('    <' + 'script src="' + libs.chartJs + '"></' + 'script>')
  scriptParts.push('    <' + 'script src="' + libs.echarts + '"></' + 'script>')
  scriptParts.push('</' + 'head>')
  const scriptTags = scriptParts.join('\n')
  return html.replace('</' + 'head>', scriptTags)
}

const onIframeLoad = (): void => {
  loading.value = false

  // 如果是自动高度，尝试调整iframe高度以适应内容
  if (props.height === 'auto' && previewFrame.value) {
    try {
      const iframe = previewFrame.value
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (iframeDoc) {
        // 等待内容渲染完成
        setTimeout(() => {
          const body = iframeDoc.body
          const html = iframeDoc.documentElement
          if (body && html) {
            const contentHeight = Math.max(
              body.scrollHeight,
              body.offsetHeight,
              html.clientHeight,
              html.scrollHeight,
              html.offsetHeight
            )

            // 设置合理的高度范围
            const minHeight = parseInt(props.minHeight) || 300
            const maxHeight = parseInt(props.maxHeight) || 600
            const finalHeight = Math.min(Math.max(contentHeight + 20, minHeight), maxHeight)

            iframe.style.height = finalHeight + 'px'
          }
        }, 100)
      }
    } catch (error) {
      console.warn('无法自动调整iframe高度:', error)
    }
  }
}

const refreshPreview = (): void => {
  loading.value = true
  if (previewFrame.value) {
    previewFrame.value.src = previewFrame.value.src
  }
}



const openModal = (): void => {
  showModal.value = true
}

const copyHtml = async (): Promise<void> => {
  try {
    await navigator.clipboard.writeText(props.htmlContent)
    ElMessage.success('HTML内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 监听HTML内容变化，重置加载状态
watch(() => props.htmlContent, () => {
  if (props.htmlContent) {
    loading.value = true
  }
}, { immediate: true })
</script>

<style scoped>
.html-preview-container {
  width: 100%;
  margin: 16px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: white;
  position: relative;
  min-width: 0; /* 确保可以收缩 */
  max-width: 100%; /* 默认不超出父容器 */
}



.html-wrapper {
  width: 100%;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e1e5e9;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.traffic-lights {
  display: flex;
  gap: 6px;
}

.light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.light.red {
  background: #ff5f57;
}

.light.yellow {
  background: #ffbd2e;
}

.light.green {
  background: #28ca42;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.header-right {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  transform: translateY(-1px);
}



.iframe-container {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  min-height: v-bind(minHeight);
  max-height: v-bind(maxHeight);
  height: v-bind(height);
  resize: vertical;
  overflow: hidden;
}

.iframe-container.auto-height {
  height: auto;
  min-height: v-bind(minHeight);
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  font-size: 14px;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #f56565;
  font-size: 14px;
}
</style>