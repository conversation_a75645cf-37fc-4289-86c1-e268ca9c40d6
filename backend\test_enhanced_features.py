"""
测试增强功能的综合测试脚本
包括真实AI集成、文档处理、向量搜索、缓存等功能
"""
import asyncio
import aiohttp
import json
import sys
import os
import tempfile
from pathlib import Path

BASE_URL = "http://localhost:8000"

class EnhancedFeatureTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.test_kb_id = None
        self.test_session_id = None
        self.test_doc_id = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username: str, password: str) -> str:
        """登录并获取token"""
        login_data = {
            "username": username,
            "password": password
        }
        
        url = f"{BASE_URL}/api/auth/login"
        try:
            async with self.session.post(url, json=login_data) as response:
                if response.status == 200:
                    result = await response.json()
                    token = result.get("access_token")
                    print(f"✅ 登录成功: {username}")
                    return token
                else:
                    text = await response.text()
                    print(f"❌ 登录失败: {username} - {text}")
                    return None
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return None
    
    async def test_redis_connection(self):
        """测试Redis连接"""
        print("\n🔗 测试Redis连接:")
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            r.ping()
            print("✅ Redis连接正常")
            
            # 测试基本操作
            r.set("test_key", "test_value", ex=10)
            value = r.get("test_key")
            if value == "test_value":
                print("✅ Redis读写操作正常")
            else:
                print("❌ Redis读写操作异常")
            
            r.delete("test_key")
            return True
            
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    async def test_ai_models_api(self):
        """测试AI模型API"""
        print("\n🤖 测试AI模型API:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        async with self.session.get(f"{BASE_URL}/api/ai/models", headers=headers) as response:
            if response.status == 200:
                models = await response.json()
                print(f"✅ 获取AI模型列表成功 - 共 {len(models)} 个模型")
                
                # 检查硅基流动模型
                siliconflow_models = [m for m in models if '硅基流动' in str(m)]
                if siliconflow_models:
                    print(f"✅ 找到硅基流动模型")
                else:
                    print("⚠️ 未找到硅基流动模型")
                return True
            else:
                print(f"❌ 获取AI模型列表失败 - 状态码: {response.status}")
                return False
    
    async def test_knowledge_base_operations(self):
        """测试知识库操作"""
        print("\n📚 测试知识库操作:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # 创建知识库
        kb_data = {
            "name": "增强功能测试知识库",
            "description": "用于测试增强功能的知识库"
        }
        
        async with self.session.post(f"{BASE_URL}/api/knowledge-bases/", 
                                   json=kb_data, headers=headers) as response:
            if response.status in [200, 201]:
                result = await response.json()
                self.test_kb_id = result.get("id")
                print(f"✅ 创建知识库成功 - ID: {self.test_kb_id}")
                return True
            else:
                print(f"❌ 创建知识库失败 - 状态码: {response.status}")
                return False
    
    async def test_document_upload_and_processing(self):
        """测试文档上传和处理"""
        print("\n📄 测试文档上传和处理:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # 创建测试文档
        test_content = """
        这是一个测试文档。
        
        人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
        
        机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。
        
        深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。
        """
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            # 上传文档
            with open(temp_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='test_document.txt', content_type='text/plain')
                
                async with self.session.post(
                    f"{BASE_URL}/api/documents/upload/{self.test_kb_id}",
                    data=data,
                    headers=headers
                ) as response:
                    if response.status in [200, 201]:
                        result = await response.json()
                        self.test_doc_id = result.get("id")
                        print(f"✅ 文档上传成功 - ID: {self.test_doc_id}")
                        print(f"✅ 文档状态: {result.get('status')}")
                        
                        # 如果文档还未处理，尝试手动触发向量化
                        if result.get('status') != 'completed':
                            await self.test_document_vectorization()
                        
                        return True
                    else:
                        text = await response.text()
                        print(f"❌ 文档上传失败 - 状态码: {response.status}, 响应: {text}")
                        return False
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    async def test_document_vectorization(self):
        """测试文档向量化"""
        print("\n🔍 测试文档向量化:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        if not self.test_doc_id:
            print("❌ 没有可用的测试文档ID")
            return False
        
        async with self.session.post(
            f"{BASE_URL}/api/documents/{self.test_doc_id}/vectorize",
            headers=headers
        ) as response:
            if response.status == 200:
                result = await response.json()
                print(f"✅ 文档向量化成功")
                print(f"✅ 处理状态: {result.get('status')}")
                if 'chunks_count' in result:
                    print(f"✅ 生成分块数量: {result.get('chunks_count')}")
                return True
            else:
                text = await response.text()
                print(f"❌ 文档向量化失败 - 状态码: {response.status}, 响应: {text}")
                return False
    
    async def test_enhanced_chat(self):
        """测试增强的聊天功能"""
        print("\n💬 测试增强聊天功能:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # 创建聊天会话
        session_data = {"title": "增强功能测试聊天"}
        
        async with self.session.post(f"{BASE_URL}/api/chat/sessions", 
                                   json=session_data, headers=headers) as response:
            if response.status in [200, 201]:
                result = await response.json()
                self.test_session_id = result.get("id")
                print(f"✅ 创建聊天会话成功 - ID: {self.test_session_id}")
            else:
                print(f"❌ 创建聊天会话失败")
                return False
        
        # 测试带知识库的流式聊天
        chat_data = {
            "message": "什么是人工智能？请详细解释。",
            "model_id": "Qwen/Qwen2.5-7B-Instruct",
            "knowledge_base_ids": [self.test_kb_id] if self.test_kb_id else []
        }
        
        print("🔄 测试流式聊天...")
        try:
            async with self.session.post(
                f"{BASE_URL}/api/chat/sessions/{self.test_session_id}/stream",
                json=chat_data, 
                headers=headers
            ) as response:
                if response.status == 200:
                    print("✅ 流式聊天连接成功")
                    
                    content_received = ""
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                if data.get('type') == 'content':
                                    content_received += data.get('content', '')
                                elif data.get('type') == 'error':
                                    print(f"⚠️ AI响应错误: {data.get('content')}")
                                elif data.get('type') == 'done':
                                    break
                            except json.JSONDecodeError:
                                continue
                    
                    if content_received:
                        print(f"✅ 接收到AI响应: {content_received[:100]}...")
                        if len(content_received) > 100:
                            print("✅ 响应内容完整")
                        return True
                    else:
                        print("⚠️ 未接收到AI响应内容")
                        return False
                else:
                    text = await response.text()
                    print(f"❌ 流式聊天失败 - 状态码: {response.status}, 响应: {text}")
                    return False
        except Exception as e:
            print(f"❌ 流式聊天错误: {e}")
            return False
    
    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据:")
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # 删除测试聊天会话
        if self.test_session_id:
            async with self.session.delete(
                f"{BASE_URL}/api/chat/sessions/{self.test_session_id}",
                headers=headers
            ) as response:
                if response.status == 200:
                    print("✅ 删除测试聊天会话成功")
        
        # 删除测试知识库
        if self.test_kb_id:
            async with self.session.delete(
                f"{BASE_URL}/api/knowledge-bases/{self.test_kb_id}",
                headers=headers
            ) as response:
                if response.status == 200:
                    print("✅ 删除测试知识库成功")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试增强功能...\n")
        
        # 测试Redis连接
        redis_ok = await self.test_redis_connection()
        if not redis_ok:
            print("❌ Redis连接失败，某些功能可能无法正常工作")
        
        # 登录
        self.auth_token = await self.login("testuser", "test123")
        if not self.auth_token:
            print("❌ 登录失败，无法继续测试")
            return
        
        try:
            # 测试各项功能
            await self.test_ai_models_api()
            await self.test_knowledge_base_operations()
            await self.test_document_upload_and_processing()
            await self.test_enhanced_chat()
            
        finally:
            # 清理测试数据
            await self.cleanup()
        
        print("\n🎉 所有增强功能测试完成！")

async def main():
    """主函数"""
    print("正在启动增强功能测试...")
    print(f"目标服务器: {BASE_URL}")
    print("请确保后端服务和Redis正在运行\n")
    
    async with EnhancedFeatureTester() as tester:
        await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        sys.exit(1)
