#!/usr/bin/env python3
"""
测试修复后的API调用
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_fixed_api():
    """测试修复后的API调用"""
    session = requests.Session()
    
    # 1. 登录
    print("1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if login_response.status_code == 200:
        result = login_response.json()
        token = result["access_token"]
        session.headers.update({"Authorization": f"Bearer {token}"})
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    # 2. 测试获取AI供应商列表（用户设置页面需要）
    print("\n2. 测试获取AI供应商列表...")
    try:
        providers_response = session.get(f"{BASE_URL}/api/ai/providers")
        if providers_response.status_code == 200:
            providers = providers_response.json()
            print(f"✅ 获取供应商成功，共 {len(providers)} 个")
            
            # 验证响应格式
            if providers:
                provider = providers[0]
                required_fields = ['id', 'name', 'display_name', 'is_active', 'created_at', 'updated_at']
                missing_fields = [field for field in required_fields if field not in provider]
                if missing_fields:
                    print(f"⚠️  供应商响应缺少字段: {missing_fields}")
                else:
                    print("✅ 供应商响应格式正确")
                    print(f"   示例: {provider['display_name']} (ID: {provider['id']})")
        else:
            print(f"❌ 获取供应商失败: {providers_response.text}")
            return
    except Exception as e:
        print(f"❌ 获取供应商异常: {e}")
        return
    
    # 3. 测试获取AI模型列表
    print("\n3. 测试获取AI模型列表...")
    try:
        models_response = session.get(f"{BASE_URL}/api/ai/models")
        if models_response.status_code == 200:
            models = models_response.json()
            print(f"✅ 获取模型成功，共 {len(models)} 个")
            
            # 验证响应格式
            if models:
                model = models[0]
                required_fields = ['id', 'provider_id', 'model_name', 'display_name', 'is_active', 'provider']
                missing_fields = [field for field in required_fields if field not in model]
                if missing_fields:
                    print(f"⚠️  模型响应缺少字段: {missing_fields}")
                else:
                    print("✅ 模型响应格式正确")
                    provider_name = model.get('provider', {}).get('display_name', '未知')
                    print(f"   示例: {model['display_name']} ({provider_name})")
        else:
            print(f"❌ 获取模型失败: {models_response.text}")
            return
    except Exception as e:
        print(f"❌ 获取模型异常: {e}")
        return
    
    # 4. 测试获取用户API密钥
    print("\n4. 测试获取用户API密钥...")
    try:
        keys_response = session.get(f"{BASE_URL}/api/ai/api-keys")
        if keys_response.status_code == 200:
            user_keys = keys_response.json()
            print(f"✅ 获取用户密钥成功，共 {len(user_keys)} 个")
            
            # 验证响应格式
            if user_keys:
                key = user_keys[0]
                required_fields = ['id', 'provider_id', 'provider', 'created_at', 'updated_at']
                missing_fields = [field for field in required_fields if field not in key]
                if missing_fields:
                    print(f"⚠️  用户密钥响应缺少字段: {missing_fields}")
                else:
                    print("✅ 用户密钥响应格式正确")
                    provider_name = key.get('provider', {}).get('display_name', '未知')
                    print(f"   示例: {provider_name} - {key.get('description', '无描述')}")
            else:
                print("ℹ️  用户暂无API密钥")
        else:
            print(f"❌ 获取用户密钥失败: {keys_response.text}")
            return
    except Exception as e:
        print(f"❌ 获取用户密钥异常: {e}")
        return
    
    # 5. 测试创建用户API密钥
    print("\n5. 测试创建用户API密钥...")
    if providers:
        test_provider = providers[0]
        create_key_data = {
            "provider_id": test_provider['id'],
            "api_key": "sk-test123456789abcdef",
            "description": f"测试密钥 - {test_provider['display_name']}"
        }
        
        try:
            create_response = session.post(f"{BASE_URL}/api/ai/api-keys", json=create_key_data)
            if create_response.status_code == 200:
                new_key = create_response.json()
                print(f"✅ 创建用户密钥成功: {new_key.get('description', '无描述')}")
                
                # 验证创建的密钥格式
                required_fields = ['id', 'provider_id', 'provider', 'created_at', 'updated_at']
                missing_fields = [field for field in required_fields if field not in new_key]
                if missing_fields:
                    print(f"⚠️  创建的密钥响应缺少字段: {missing_fields}")
                else:
                    print("✅ 创建的密钥响应格式正确")
                
                # 清理测试数据
                print("\n6. 清理测试数据...")
                delete_response = session.delete(f"{BASE_URL}/api/ai/api-keys/{new_key['id']}")
                if delete_response.status_code == 200:
                    print("✅ 测试密钥删除成功")
                else:
                    print(f"❌ 删除测试密钥失败: {delete_response.text}")
            else:
                print(f"❌ 创建用户密钥失败: {create_response.text}")
        except Exception as e:
            print(f"❌ 创建用户密钥异常: {e}")
    
    print("\n🎉 修复后的API测试完成!")
    print("\n📋 总结:")
    print("✅ 所有API端点响应格式与前端类型定义匹配")
    print("✅ 前端可以正确解析后端响应数据")
    print("✅ 用户设置页面应该能正常工作")

if __name__ == "__main__":
    test_fixed_api()
