#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import Session, text
from app.core.database import engine

def main():
    """检查数据库表结构"""
    print("检查数据库表结构...")
    
    with Session(engine) as session:
        # 检查ai_providers表结构
        result = session.exec(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'ai_providers' ORDER BY ordinal_position"))
        print("\nai_providers表结构:")
        for row in result:
            print(f"  {row[0]}: {row[1]}")
        
        # 检查ai_models表是否存在
        result = session.exec(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'ai_models' ORDER BY ordinal_position"))
        print("\nai_models表结构:")
        for row in result:
            print(f"  {row[0]}: {row[1]}")

if __name__ == "__main__":
    main()
