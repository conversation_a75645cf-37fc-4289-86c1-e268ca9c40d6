#!/usr/bin/env python3
"""
调试删除API问题
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def debug_delete_issue():
    """调试删除问题"""
    print("🔍 调试删除API问题...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 1. 获取所有供应商
        print("\n📋 获取所有供应商...")
        response = requests.get(f"{BASE_URL}/admin/ai-providers", headers=headers)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ 获取供应商成功: {len(providers)} 个")
            
            for provider in providers:
                print(f"  - ID: {provider['id']}, 名称: {provider['display_name']}, 活跃: {provider['is_active']}")
                
            # 2. 检查ID 6是否存在
            provider_6 = next((p for p in providers if p['id'] == 6), None)
            if provider_6:
                print(f"\n🎯 找到ID 6的供应商: {provider_6['display_name']}")
                
                # 3. 检查该供应商下是否有模型
                print("📋 检查该供应商下的模型...")
                models_response = requests.get(f"{BASE_URL}/admin/ai-models", headers=headers)
                if models_response.status_code == 200:
                    models = models_response.json()
                    provider_6_models = [m for m in models if m['provider_id'] == 6]
                    print(f"  该供应商下有 {len(provider_6_models)} 个模型")
                    
                    for model in provider_6_models:
                        print(f"    - 模型: {model['display_name']} (ID: {model['id']})")
                    
                    if provider_6_models:
                        print("⚠️  该供应商下有模型，无法直接删除")
                        print("💡 建议：先删除相关模型，再删除供应商")
                        
                        # 尝试删除一个模型
                        if provider_6_models:
                            model_to_delete = provider_6_models[0]
                            print(f"\n🗑️  尝试删除模型: {model_to_delete['display_name']}")
                            
                            delete_response = requests.delete(
                                f"{BASE_URL}/admin/ai-models/{model_to_delete['id']}", 
                                headers=headers
                            )
                            print(f"删除模型响应状态: {delete_response.status_code}")
                            print(f"删除模型响应内容: {delete_response.text}")
                    else:
                        # 4. 尝试删除供应商
                        print(f"\n🗑️  尝试删除供应商: {provider_6['display_name']}")
                        delete_response = requests.delete(f"{BASE_URL}/admin/ai-providers/6", headers=headers)
                        print(f"删除响应状态: {delete_response.status_code}")
                        print(f"删除响应内容: {delete_response.text}")
                        
                        if delete_response.status_code == 200:
                            print("✅ 删除成功")
                        else:
                            print("❌ 删除失败")
            else:
                print("❌ 未找到ID 6的供应商")
                
        else:
            print(f"❌ 获取供应商失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")

if __name__ == "__main__":
    debug_delete_issue()
