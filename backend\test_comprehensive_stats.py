"""
综合测试统计API功能
测试各种统计场景和边界情况
"""
import requests
import tempfile
import os
import json
import time

BASE_URL = "http://localhost:8000"

def test_comprehensive_stats():
    """综合测试统计API功能"""
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {"username": "testuser", "password": "test123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 测试空数据状态的统计
    print("\n📊 测试空数据状态统计...")
    
    # 先清理可能存在的数据
    print("🧹 清理现有数据...")
    
    # 获取现有知识库并删除
    response = requests.get(f"{BASE_URL}/api/knowledge-bases/", headers=headers)
    if response.status_code == 200:
        kbs = response.json()
        for kb in kbs:
            requests.delete(f"{BASE_URL}/api/knowledge-bases/{kb['id']}", headers=headers)
    
    # 获取现有聊天会话并删除
    response = requests.get(f"{BASE_URL}/api/chat/sessions", headers=headers)
    if response.status_code == 200:
        sessions = response.json()
        for session in sessions:
            requests.delete(f"{BASE_URL}/api/chat/sessions/{session['id']}", headers=headers)
    
    # 测试空状态统计
    response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
    if response.status_code == 200:
        stats = response.json()
        print("✅ 空状态统计测试成功:")
        print(f"   知识库数量: {stats['knowledge_bases']} (应为0)")
        print(f"   文档数量: {stats['documents']} (应为0)")
        print(f"   聊天会话数量: {stats['chat_sessions']} (应为0)")
        print(f"   存储使用: {stats['storage_used']} GB (应为0)")
    
    # 3. 逐步创建数据并测试统计变化
    print("\n📈 测试数据增长统计...")
    
    created_kbs = []
    created_sessions = []
    
    # 创建多个知识库
    for i in range(3):
        kb_data = {"name": f"测试知识库{i+1}", "description": f"第{i+1}个测试知识库"}
        response = requests.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=headers)
        
        if response.status_code in [200, 201]:
            kb_id = response.json().get("id")
            created_kbs.append(kb_id)
            print(f"✅ 创建知识库{i+1} - ID: {kb_id}")
            
            # 测试当前统计
            response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
            if response.status_code == 200:
                stats = response.json()
                print(f"   当前知识库数量: {stats['knowledge_bases']}")
    
    # 为每个知识库上传文档
    total_docs = 0
    for i, kb_id in enumerate(created_kbs):
        # 创建不同大小的测试文档
        file_contents = [
            f"这是知识库{i+1}的文档1。" + "内容" * 100,  # 较大文档
            f"这是知识库{i+1}的文档2。" + "数据" * 50,   # 中等文档
            f"这是知识库{i+1}的文档3。简短内容。"        # 小文档
        ]
        
        temp_files = []
        for j, content in enumerate(file_contents):
            temp_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix=f'_kb{i+1}_doc{j+1}.txt', 
                delete=False, 
                encoding='utf-8'
            )
            temp_file.write(content)
            temp_file.close()
            temp_files.append(temp_file.name)
        
        try:
            # 批量上传
            files = []
            for temp_file in temp_files:
                files.append(('files', (os.path.basename(temp_file), open(temp_file, 'rb'), 'text/plain')))
            
            response = requests.post(f"{BASE_URL}/api/documents/batch-upload/{kb_id}", files=files, headers=headers)
            
            # 关闭文件
            for _, (_, file_obj, _) in files:
                file_obj.close()
            
            if response.status_code == 200:
                result = response.json()
                docs_uploaded = len(result['uploaded_documents'])
                total_docs += docs_uploaded
                print(f"✅ 知识库{i+1}上传 {docs_uploaded} 个文档")
                
                # 测试存储统计
                response = requests.get(f"{BASE_URL}/api/stats/detailed", headers=headers)
                if response.status_code == 200:
                    detailed_stats = response.json()
                    print(f"   当前存储使用: {detailed_stats['storage_used_bytes']} bytes")
        
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
    
    # 创建多个聊天会话
    for i in range(4):
        session_data = {"title": f"测试聊天会话{i+1}"}
        response = requests.post(f"{BASE_URL}/api/chat/sessions", json=session_data, headers=headers)
        
        if response.status_code in [200, 201]:
            session_id = response.json().get("id")
            created_sessions.append(session_id)
            print(f"✅ 创建聊天会话{i+1} - ID: {session_id}")
            
            # 为每个会话发送几条消息
            for j in range(2):
                chat_data = {
                    "message": f"这是会话{i+1}的第{j+1}条消息",
                    "model_id": "Qwen/Qwen2.5-7B-Instruct",
                    "knowledge_base_ids": created_kbs[:1] if created_kbs else []
                }
                
                response = requests.post(f"{BASE_URL}/api/chat/sessions/{session_id}/stream", 
                                       json=chat_data, headers=headers, stream=True)
                
                if response.status_code == 200:
                    # 简单读取响应
                    for line in response.iter_lines():
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                try:
                                    data = json.loads(line_str[6:])
                                    if data.get('type') == 'done':
                                        break
                                except json.JSONDecodeError:
                                    continue
                
                time.sleep(0.1)  # 避免请求过快
    
    # 4. 测试最终统计结果
    print("\n📊 测试最终统计结果...")
    
    # 基础统计
    response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
    if response.status_code == 200:
        stats = response.json()
        print("✅ 最终基础统计:")
        print(f"   知识库数量: {stats['knowledge_bases']}")
        print(f"   文档数量: {stats['documents']}")
        print(f"   聊天会话数量: {stats['chat_sessions']}")
        print(f"   存储使用: {stats['storage_used']} GB")
        print(f"   存储使用率: {stats['storage_percent']}%")
    
    # 详细统计
    response = requests.get(f"{BASE_URL}/api/stats/detailed", headers=headers)
    if response.status_code == 200:
        detailed_stats = response.json()
        print("\n✅ 最终详细统计:")
        print(f"   知识库数量: {detailed_stats['knowledge_bases']}")
        print(f"   文档数量: {detailed_stats['documents']}")
        print(f"   聊天会话数量: {detailed_stats['chat_sessions']}")
        print(f"   消息总数: {detailed_stats['total_messages']}")
        print(f"   存储使用: {detailed_stats['storage_used_bytes']} bytes")
        print(f"   存储使用: {detailed_stats['storage_used_mb']} MB")
        print(f"   存储使用: {detailed_stats['storage_used_gb']} GB")
        print("   最近7天活动:")
        for key, value in detailed_stats['recent_activity'].items():
            print(f"     {key}: {value}")
    
    # 活动统计
    response = requests.get(f"{BASE_URL}/api/stats/activity?days=3", headers=headers)
    if response.status_code == 200:
        activity_stats = response.json()
        print("\n✅ 最近3天活动统计:")
        for day_stats in activity_stats:
            print(f"   {day_stats['date']}:")
            print(f"     知识库创建: {day_stats['knowledge_bases_created']}")
            print(f"     文档上传: {day_stats['documents_uploaded']}")
            print(f"     会话创建: {day_stats['chat_sessions_created']}")
            print(f"     消息发送: {day_stats['messages_sent']}")
    
    # 5. 测试边界情况
    print("\n🔍 测试边界情况...")
    
    # 测试大天数参数
    response = requests.get(f"{BASE_URL}/api/stats/activity?days=30", headers=headers)
    if response.status_code == 200:
        activity_stats = response.json()
        print(f"✅ 30天活动统计成功 - 共 {len(activity_stats)} 条记录")
    
    # 测试小天数参数
    response = requests.get(f"{BASE_URL}/api/stats/activity?days=1", headers=headers)
    if response.status_code == 200:
        activity_stats = response.json()
        print(f"✅ 1天活动统计成功 - 共 {len(activity_stats)} 条记录")
    
    # 6. 验证数据一致性
    print("\n🔍 验证数据一致性...")
    
    # 获取基础统计和详细统计，比较数据一致性
    response1 = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
    response2 = requests.get(f"{BASE_URL}/api/stats/detailed", headers=headers)
    
    if response1.status_code == 200 and response2.status_code == 200:
        basic_stats = response1.json()
        detailed_stats = response2.json()
        
        consistency_check = True
        
        if basic_stats['knowledge_bases'] != detailed_stats['knowledge_bases']:
            print("❌ 知识库数量不一致")
            consistency_check = False
        
        if basic_stats['documents'] != detailed_stats['documents']:
            print("❌ 文档数量不一致")
            consistency_check = False
        
        if basic_stats['chat_sessions'] != detailed_stats['chat_sessions']:
            print("❌ 聊天会话数量不一致")
            consistency_check = False
        
        if abs(basic_stats['storage_used'] - detailed_stats['storage_used_gb']) > 0.01:
            print("❌ 存储使用量不一致")
            consistency_check = False
        
        if consistency_check:
            print("✅ 数据一致性检查通过")
        else:
            print("❌ 数据一致性检查失败")
    
    print("\n🎉 综合统计API测试完成！")
    print(f"📊 测试总结:")
    print(f"   创建了 {len(created_kbs)} 个知识库")
    print(f"   创建了 {len(created_sessions)} 个聊天会话")
    print(f"   上传了多个文档")
    print(f"   发送了多条消息")
    print(f"   所有统计API都正常工作")

if __name__ == "__main__":
    test_comprehensive_stats()
