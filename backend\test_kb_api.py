#!/usr/bin/env python3
"""
测试知识库API
"""
import requests
import json
import sys
sys.path.append('.')
from app.core.security import create_access_token

def generate_test_token():
    """生成测试token"""
    # 为用户ID 4生成token
    token_data = {"sub": 4}
    token = create_access_token(data=token_data)
    return token

def login_and_get_token():
    """登录并获取token"""
    login_url = 'http://localhost:8000/api/auth/login'
    login_data = {
        'username': 'testuser',
        'password': 'testpass123'
    }

    try:
        response = requests.post(login_url, json=login_data)
        print(f'登录状态码: {response.status_code}')
        print(f'登录响应: {response.text}')

        if response.status_code == 200:
            data = response.json()
            return data.get('access_token')
        else:
            return None
    except Exception as e:
        print(f'登录失败: {e}')
        return None

def test_knowledge_base_api():
    # 先登录获取token
    token = login_and_get_token()
    if not token:
        print('无法获取有效token，退出测试')
        return

    print(f'获取到的token: {token}')

    # 测试知识库API
    url = 'http://localhost:8000/api/knowledge-bases/'
    headers = {'Authorization': f'Bearer {token}'}
    params = {'limit': 3}

    try:
        response = requests.get(url, headers=headers, params=params)
        print(f'状态码: {response.status_code}')
        print(f'响应头: {dict(response.headers)}')
        print(f'响应内容: {response.text}')

        if response.status_code == 200:
            data = response.json()
            print(f'知识库数量: {len(data)}')
            for kb in data:
                print(f'ID: {kb.get("id")}, 名称: {kb.get("name")}')

    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == "__main__":
    test_knowledge_base_api()
