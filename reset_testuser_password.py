#!/usr/bin/env python3
import requests

# 用管理员身份重置testuser密码
login_data = {'username': 'admin', 'password': 'admin123'}
response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
if response.status_code == 200:
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 重置testuser密码
    update_data = {
        'password': 'test123'
    }
    
    # 假设有更新用户密码的API端点
    response = requests.put('http://localhost:8000/api/admin/users/4/password', json=update_data, headers=headers)
    print(f"重置密码响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        print("密码重置成功，现在测试登录...")
        
        # 测试登录
        test_login = {'username': 'testuser', 'password': 'test123'}
        login_response = requests.post('http://localhost:8000/api/auth/login', json=test_login)
        print(f"登录测试: {login_response.status_code}")
        if login_response.status_code == 200:
            print("✅ testuser登录成功！")
        else:
            print(f"❌ testuser登录失败: {login_response.text}")
    
else:
    print('管理员登录失败')
