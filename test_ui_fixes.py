#!/usr/bin/env python3
"""
测试用户界面修复效果
验证图表下方的统计数字是否使用真实数据而非硬编码
"""

import requests
import json
from datetime import datetime, timedelta

# API基础URL
BASE_URL = "http://localhost:8000"

def test_user_stats_api():
    """测试用户统计API是否返回正确的数据结构"""
    print("🧪 测试用户统计API...")
    
    # 登录获取token
    login_data = {
        "username": "testuser",
        "password": "test123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"  ❌ 登录失败: {response.status_code}")
            return False

        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 测试基础统计
        response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
        if response.status_code == 200:
            stats = response.json()
            print(f"  ✅ 基础统计: 知识库 {stats['knowledge_bases']}, 文档 {stats['documents']}, 聊天 {stats['chat_sessions']}")
        else:
            print(f"  ❌ 获取基础统计失败: {response.status_code}")
            return False

        # 测试详细统计
        response = requests.get(f"{BASE_URL}/api/stats/detailed", headers=headers)
        if response.status_code == 200:
            detailed = response.json()
            print(f"  ✅ 详细统计: 存储使用 {detailed['storage_used_mb']}MB")
            print(f"    - 最近活动: {detailed['recent_activity']}")
        else:
            print(f"  ❌ 获取详细统计失败: {response.status_code}")
            return False

        # 测试活动统计
        response = requests.get(f"{BASE_URL}/api/stats/activity?days=7", headers=headers)
        if response.status_code == 200:
            activity = response.json()
            print(f"  ✅ 活动统计: 获取到 {len(activity)} 天的数据")
            if activity:
                today = activity[-1]  # 最后一天的数据
                print(f"    - 今日数据: 文档 {today['documents_uploaded']}, 聊天 {today['chat_sessions_created']}, 知识库 {today['knowledge_bases_created']}")
        else:
            print(f"  ❌ 获取活动统计失败: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_admin_stats_api():
    """测试管理员统计API"""
    print("🧪 测试管理员统计API...")
    
    # 管理员登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"  ❌ 管理员登录失败: {response.status_code}")
            return False

        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 测试系统统计
        response = requests.get(f"{BASE_URL}/api/admin/stats", headers=headers)
        if response.status_code == 200:
            stats = response.json()
            print(f"  ✅ 系统统计: 用户 {stats['total_users']}, 知识库 {stats['total_knowledge_bases']}")
            print(f"    - 文档 {stats['total_documents']}, 聊天 {stats['total_chat_sessions']}")
            print(f"    - 存储 {stats['total_storage_mb']}MB")
        else:
            print(f"  ❌ 获取系统统计失败: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def create_test_data():
    """创建一些测试数据来验证统计功能"""
    print("🧪 创建测试数据...")
    
    # 登录获取token
    login_data = {
        "username": "testuser",
        "password": "test123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"  ❌ 登录失败: {response.status_code}")
            return False

        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 创建知识库
        kb_data = {
            "name": f"测试知识库_{datetime.now().strftime('%H%M%S')}",
            "description": "用于测试统计功能的知识库"
        }
        response = requests.post(f"{BASE_URL}/api/knowledge-bases", json=kb_data, headers=headers)
        if response.status_code == 201:
            kb = response.json()
            print(f"  ✅ 创建知识库: {kb['name']}")
        else:
            print(f"  ⚠️ 创建知识库失败: {response.status_code}")

        # 创建聊天会话
        chat_data = {
            "title": f"测试对话_{datetime.now().strftime('%H%M%S')}",
            "model_id": 1,
            "knowledge_base_ids": [],
            "context_length": 10
        }
        response = requests.post(f"{BASE_URL}/api/chat/sessions", json=chat_data, headers=headers)
        if response.status_code == 201:
            session = response.json()
            print(f"  ✅ 创建聊天会话: {session['title']}")
        else:
            print(f"  ⚠️ 创建聊天会话失败: {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 创建测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始测试用户界面数据修复...")
    print("=" * 60)
    
    results = []
    
    # 创建测试数据
    results.append(("创建测试数据", create_test_data()))
    
    # 测试用户统计API
    results.append(("用户统计API", test_user_stats_api()))
    
    # 测试管理员统计API
    results.append(("管理员统计API", test_admin_stats_api()))
    
    # 输出结果
    print("=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有API测试通过！")
        print("\n📝 前端修复说明:")
        print("  - 图表下方的统计数字现在使用真实API数据")
        print("  - 存储使用分布显示真实的用户配额数据")
        print("  - 最近7天活动显示真实的活动统计")
        print("  - 统计卡片中的增长数据使用真实的最近7天数据")
        print("  - 新建对话时AI模型选择器显示正确的模型")
    else:
        print("⚠️ 部分测试失败，请检查API服务")

if __name__ == "__main__":
    main()
