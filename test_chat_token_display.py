#!/usr/bin/env python3
"""
测试聊天界面token显示
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_chat_token_display():
    """测试聊天界面token显示"""
    print("🧪 测试聊天界面token显示...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 1. 获取AI模型列表（用户可见的）
        print("\n📋 获取用户可见的AI模型列表...")
        response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ 获取模型列表成功: {len(models)} 个模型")
            
            # 显示每个模型的token信息
            for model in models:
                token_info = f"{model['max_tokens']} tokens" if model['max_tokens'] else "无限制"
                status_info = "可用" if model['is_active'] else "已禁用"
                print(f"  - {model['display_name']}: {token_info} ({status_info})")
                
            # 2. 设置一个模型为无限制
            if models:
                test_model = models[0]
                print(f"\n🔧 设置模型 {test_model['display_name']} 为无限制...")
                
                # 使用管理员API设置
                update_data = {"max_tokens": None}
                response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model['id']}", json=update_data, headers=headers)
                if response.status_code == 200:
                    print("✅ 设置无限制成功")
                    
                    # 验证用户API返回的数据
                    response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
                    if response.status_code == 200:
                        updated_models = response.json()
                        updated_model = next((m for m in updated_models if m['id'] == test_model['id']), None)
                        if updated_model:
                            token_info = f"{updated_model['max_tokens']} tokens" if updated_model['max_tokens'] else "无限制"
                            print(f"✅ 验证成功：{updated_model['display_name']} - {token_info}")
                        else:
                            print("❌ 找不到更新后的模型")
                else:
                    print(f"❌ 设置无限制失败: {response.status_code}")
                
                # 3. 设置另一个模型为自定义值
                if len(models) > 1:
                    test_model2 = models[1]
                    print(f"\n🔧 设置模型 {test_model2['display_name']} 为4000 tokens...")
                    
                    update_data = {"max_tokens": 4000}
                    response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model2['id']}", json=update_data, headers=headers)
                    if response.status_code == 200:
                        print("✅ 设置自定义值成功")
                        
                        # 验证用户API返回的数据
                        response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
                        if response.status_code == 200:
                            updated_models = response.json()
                            updated_model = next((m for m in updated_models if m['id'] == test_model2['id']), None)
                            if updated_model:
                                token_info = f"{updated_model['max_tokens']} tokens" if updated_model['max_tokens'] else "无限制"
                                print(f"✅ 验证成功：{updated_model['display_name']} - {token_info}")
                            else:
                                print("❌ 找不到更新后的模型")
                    else:
                        print(f"❌ 设置自定义值失败: {response.status_code}")
                
                # 4. 测试禁用一个模型
                if len(models) > 2:
                    test_model3 = models[2]
                    print(f"\n🔧 禁用模型 {test_model3['display_name']}...")
                    
                    update_data = {"is_active": False}
                    response = requests.put(f"{BASE_URL}/admin/ai-models/{test_model3['id']}", json=update_data, headers=headers)
                    if response.status_code == 200:
                        print("✅ 禁用模型成功")
                        
                        # 验证用户API返回的数据
                        response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
                        if response.status_code == 200:
                            updated_models = response.json()
                            updated_model = next((m for m in updated_models if m['id'] == test_model3['id']), None)
                            if updated_model:
                                status_info = "可用" if updated_model['is_active'] else "已禁用"
                                print(f"✅ 验证成功：{updated_model['display_name']} - {status_info}")
                            else:
                                print("❌ 找不到更新后的模型")
                    else:
                        print(f"❌ 禁用模型失败: {response.status_code}")
                
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
        
        print("\n🎉 测试完成！现在可以在前端查看token显示效果")
        print("📝 预期效果：")
        print("  - 无限制模型显示：Token 使用: X/无限制")
        print("  - 有限制模型显示：Token 使用: X/4000")
        print("  - 切换模型时显示提示信息")
        print("  - 禁用模型会显示警告")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_chat_token_display()
