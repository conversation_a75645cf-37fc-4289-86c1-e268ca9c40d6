#!/usr/bin/env python3
"""
创建测试用户的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models.user import User
from app.core.security import get_password_hash
from sqlalchemy.orm import Session

def create_test_user():
    """创建测试用户"""
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 检查是否已存在测试用户
        existing_user = db.query(User).filter(User.email == '<EMAIL>').first()
        if existing_user:
            print('测试用户已存在')
            print(f'用户名: {existing_user.username}')
            print(f'邮箱: {existing_user.email}')
            print(f'状态: {"激活" if existing_user.is_active else "未激活"}')
            print('密码: password123')
            return
        
        # 创建测试用户
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            hashed_password=get_password_hash('password123'),
            is_active=True,
            is_admin=False
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print('测试用户创建成功！')
        print(f'用户名: {test_user.username}')
        print(f'邮箱: {test_user.email}')
        print('密码: password123')
        print('现在您可以使用这些凭据登录了')
        
    except Exception as e:
        print(f'创建用户时出错: {e}')
        db.rollback()
    finally:
        db.close()

if __name__ == '__main__':
    create_test_user()
