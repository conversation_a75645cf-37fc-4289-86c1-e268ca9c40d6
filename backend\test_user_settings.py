#!/usr/bin/env python3
"""
测试用户设置页面的API功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_settings_api():
    """测试用户设置页面的API功能"""
    session = requests.Session()
    
    # 1. 登录
    print("1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if login_response.status_code == 200:
        result = login_response.json()
        token = result["access_token"]
        session.headers.update({"Authorization": f"Bearer {token}"})
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    # 2. 获取AI供应商列表
    print("\n2. 测试获取AI供应商列表...")
    providers_response = session.get(f"{BASE_URL}/api/ai/providers")
    if providers_response.status_code == 200:
        providers = providers_response.json()
        print(f"✅ 获取供应商成功，共 {len(providers)} 个")
        
        # 显示供应商信息
        for provider in providers:
            print(f"   - {provider['display_name']} (ID: {provider['id']}) - {provider['name']}")
        
        # 选择第一个供应商进行后续测试
        if providers:
            test_provider = providers[0]
            print(f"\n选择供应商进行测试: {test_provider['display_name']}")
        else:
            print("❌ 没有可用的供应商")
            return
    else:
        print(f"❌ 获取供应商失败: {providers_response.text}")
        return
    
    # 3. 获取AI模型列表
    print("\n3. 测试获取AI模型列表...")
    models_response = session.get(f"{BASE_URL}/api/ai/models")
    if models_response.status_code == 200:
        models = models_response.json()
        print(f"✅ 获取模型成功，共 {len(models)} 个")
        
        # 显示该供应商的模型
        provider_models = [m for m in models if m['provider_id'] == test_provider['id']]
        print(f"   供应商 {test_provider['display_name']} 的模型:")
        for model in provider_models:
            system_key = "有" if model.get('system_api_key') else "无"
            allow_system = "是" if model.get('allow_system_key_use') else "否"
            print(f"     - {model['display_name']} (系统密钥: {system_key}, 允许使用: {allow_system})")
        
        if provider_models:
            test_model = provider_models[0]
            print(f"\n选择模型进行测试: {test_model['display_name']}")
        else:
            print("❌ 该供应商没有可用的模型")
            return
    else:
        print(f"❌ 获取模型失败: {models_response.text}")
        return
    
    # 4. 测试连接（使用系统密钥）
    print("\n4. 测试AI连接（使用系统密钥）...")
    if test_model.get('allow_system_key_use') and test_model.get('system_api_key'):
        test_data = {
            "provider_id": test_provider['id'],
            "model_name": test_model['model_name']
        }
        
        connection_response = session.post(f"{BASE_URL}/api/ai/test-connection", json=test_data)
        if connection_response.status_code == 200:
            result = connection_response.json()
            if result['success']:
                print(f"✅ 连接测试成功: {result['message']}")
                if result.get('response_time'):
                    print(f"   响应时间: {result['response_time']:.3f}秒")
            else:
                print(f"❌ 连接测试失败: {result['message']}")
        else:
            print(f"❌ 连接测试请求失败: {connection_response.text}")
    else:
        print("⚠️  该模型不支持系统密钥或未配置系统密钥")
    
    # 5. 测试用户API密钥管理
    print("\n5. 测试用户API密钥管理...")
    
    # 获取现有用户密钥
    keys_response = session.get(f"{BASE_URL}/api/ai/api-keys")
    if keys_response.status_code == 200:
        user_keys = keys_response.json()
        print(f"✅ 获取用户密钥成功，共 {len(user_keys)} 个")
        
        for key in user_keys:
            provider_name = key.get('provider', {}).get('display_name', '未知')
            print(f"   - {provider_name}: {key.get('description', '无描述')}")
    else:
        print(f"❌ 获取用户密钥失败: {keys_response.text}")
    
    # 创建测试用户密钥
    print("\n6. 测试创建用户API密钥...")
    create_key_data = {
        "provider_id": test_provider['id'],
        "api_key": "sk-test123456789abcdef",
        "description": f"测试密钥 - {test_provider['display_name']}"
    }
    
    create_response = session.post(f"{BASE_URL}/api/ai/api-keys", json=create_key_data)
    if create_response.status_code == 200:
        new_key = create_response.json()
        print(f"✅ 创建用户密钥成功: {new_key['description']}")
        
        # 测试连接（使用用户密钥）
        print("\n7. 测试AI连接（使用用户密钥）...")
        test_data_user = {
            "provider_id": test_provider['id'],
            "model_name": test_model['model_name'],
            "api_key": create_key_data['api_key']
        }
        
        connection_response = session.post(f"{BASE_URL}/api/ai/test-connection", json=test_data_user)
        if connection_response.status_code == 200:
            result = connection_response.json()
            print(f"连接测试结果: {'成功' if result['success'] else '失败'}")
            print(f"消息: {result['message']}")
        else:
            print(f"❌ 连接测试请求失败: {connection_response.text}")
        
        # 清理测试数据
        print("\n8. 清理测试数据...")
        delete_response = session.delete(f"{BASE_URL}/api/ai/api-keys/{new_key['id']}")
        if delete_response.status_code == 200:
            print("✅ 测试密钥删除成功")
        else:
            print(f"❌ 删除测试密钥失败: {delete_response.text}")
    else:
        print(f"❌ 创建用户密钥失败: {create_response.text}")
    
    print("\n🎉 用户设置API测试完成!")

if __name__ == "__main__":
    test_user_settings_api()
