"""
最终的完整API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_api_docs():
    """测试API文档"""
    print("\n📚 测试API文档...")
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API文档可访问")
            return True
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")
        return False

def test_login():
    """测试登录"""
    print("\n🔐 测试登录功能...")
    
    # 测试普通用户登录
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", data={
            "username": "testuser",
            "password": "testpass123"
        })
        
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                print("✅ 普通用户登录成功")
                user_token = data['access_token']
            else:
                print(f"❌ 普通用户登录失败: {data}")
                return None, None
        else:
            print(f"❌ 普通用户登录请求失败: {response.status_code} - {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ 普通用户登录异常: {e}")
        return None, None
    
    # 测试管理员登录
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", data={
            "username": "admin",
            "password": "testpass123"
        })
        
        if response.status_code == 200:
            data = response.json()
            if 'access_token' in data:
                print("✅ 管理员登录成功")
                admin_token = data['access_token']
                return user_token, admin_token
            else:
                print(f"❌ 管理员登录失败: {data}")
                return user_token, None
        else:
            print(f"❌ 管理员登录请求失败: {response.status_code} - {response.text}")
            return user_token, None
    except Exception as e:
        print(f"❌ 管理员登录异常: {e}")
        return user_token, None

def test_user_apis(token):
    """测试用户相关API"""
    print("\n👤 测试用户API...")
    
    if not token:
        print("❌ 跳过用户API测试 - 无token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试获取用户信息
    try:
        response = requests.get(f"{BASE_URL}/api/users/me", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取用户信息成功: {data.get('username', '')}")
        else:
            print(f"❌ 获取用户信息失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取用户信息异常: {e}")
        return False
    
    # 测试获取知识库列表
    try:
        response = requests.get(f"{BASE_URL}/api/knowledge-bases", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取知识库列表成功: {len(data)} 个知识库")
        else:
            print(f"❌ 获取知识库列表失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取知识库列表异常: {e}")
        return False
    
    return True

def test_ai_apis():
    """测试AI相关API"""
    print("\n🤖 测试AI API...")
    
    # 测试获取AI模型列表
    try:
        response = requests.get(f"{BASE_URL}/api/ai/providers")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取AI模型列表成功: {len(data)} 个模型")
            for model in data:
                print(f"   - {model.get('provider_name', '')}: {model.get('model_name', '')}")
        else:
            print(f"❌ 获取AI模型列表失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取AI模型列表异常: {e}")
        return False
    
    return True

def test_chat_apis(token):
    """测试聊天相关API"""
    print("\n💬 测试聊天API...")
    
    if not token:
        print("❌ 跳过聊天API测试 - 无token")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试获取聊天会话
    try:
        response = requests.get(f"{BASE_URL}/api/chat/sessions", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取聊天会话成功: {len(data)} 个会话")
        else:
            print(f"❌ 获取聊天会话失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 获取聊天会话异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始最终API测试...")
    print("=" * 60)
    
    results = []
    
    # 基础测试
    results.append(("健康检查", test_health()))
    results.append(("API文档", test_api_docs()))
    
    # 登录测试
    user_token, admin_token = test_login()
    results.append(("用户登录", user_token is not None))
    results.append(("管理员登录", admin_token is not None))
    
    # 功能测试
    results.append(("AI模型API", test_ai_apis()))
    results.append(("用户API", test_user_apis(user_token)))
    results.append(("聊天API", test_chat_apis(user_token)))
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有API测试通过！后端服务器运行正常。")
        print("\n🌟 系统状态:")
        print("✅ 后端服务器: http://localhost:8000")
        print("✅ 前端应用: http://localhost:3001")
        print("✅ API文档: http://localhost:8000/docs")
        print("✅ 数据库: PostgreSQL 连接正常")
        print("✅ 所有API端点: 正常工作")
        
        print("\n🧪 测试账户:")
        print("- 普通用户: testuser / testpass123")
        print("- 管理员: admin / testpass123")
        
        print("\n🎯 下一步:")
        print("1. 在浏览器中访问 http://localhost:3001")
        print("2. 使用测试账户登录")
        print("3. 测试各项功能")
        print("4. 查看API文档: http://localhost:8000/docs")
        
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✨ 恭喜！AI知识库系统已完全启动并运行正常！")
    else:
        print("\n❌ 系统存在问题，请检查日志并修复。")
