#!/usr/bin/env python3
"""
测试系统设置修复
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_settings_fix():
    """测试系统设置修复"""
    print("🧪 测试系统设置修复...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # 1. 获取当前系统设置
        print("\n📋 获取当前系统设置...")
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        if response.status_code == 200:
            settings = response.json()
            print("✅ 获取设置成功")
            
            # 显示当前设置
            for setting in settings:
                print(f"  - {setting['key']}: {setting['value']} ({setting['description']})")
                
            # 找到对话历史保留天数设置
            chat_retention = next((s for s in settings if s['key'] == 'chat_retention_days'), None)
            if chat_retention:
                print(f"\n🔍 当前对话历史保留天数: {chat_retention['value']}")
                if chat_retention['value'] == '0':
                    print("✅ 对话历史保留天数设置为永久（0天）")
                else:
                    print(f"⚠️ 对话历史保留天数设置为: {chat_retention['value']}天")
            
            # 找到默认模型设置
            default_model = next((s for s in settings if s['key'] == 'default_model_id'), None)
            if default_model:
                print(f"\n🔍 当前默认模型ID: {default_model['value']}")
                
                # 获取模型列表来验证
                models_response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
                if models_response.status_code == 200:
                    models = models_response.json()
                    model = next((m for m in models if m['id'] == int(default_model['value'])), None)
                    if model:
                        print(f"✅ 默认模型: {model['display_name']} (ID: {model['id']})")
                    else:
                        print(f"❌ 找不到ID为 {default_model['value']} 的模型")
                        
        else:
            print(f"❌ 获取设置失败: {response.status_code}")
            
        # 2. 测试设置永久保留
        print("\n🔧 测试设置对话历史为永久保留...")
        update_data = {
            "value": "0"
        }
        response = requests.put(f"{BASE_URL}/admin/settings/chat_retention_days", json=update_data, headers=headers)
        if response.status_code == 200:
            print("✅ 设置永久保留成功")
            
            # 验证设置
            response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
            if response.status_code == 200:
                settings = response.json()
                chat_retention = next((s for s in settings if s['key'] == 'chat_retention_days'), None)
                if chat_retention and chat_retention['value'] == '0':
                    print("✅ 验证成功：对话历史保留天数已设置为永久（0天）")
                else:
                    print(f"❌ 验证失败：对话历史保留天数为 {chat_retention['value'] if chat_retention else 'None'}")
        else:
            print(f"❌ 设置永久保留失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_settings_fix()
