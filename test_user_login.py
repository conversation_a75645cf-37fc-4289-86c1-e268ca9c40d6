#!/usr/bin/env python3
import requests

# 测试用户登录
login_data = {'username': 'testuser', 'password': 'test123'}
response = requests.post('http://localhost:8000/api/auth/login', json=login_data)

print(f"状态码: {response.status_code}")
print(f"响应: {response.text}")

if response.status_code == 200:
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 测试获取用户统计
    stats_response = requests.get('http://localhost:8000/api/stats/dashboard', headers=headers)
    print(f"统计API状态码: {stats_response.status_code}")
    print(f"统计API响应: {stats_response.text}")
else:
    print("登录失败")
