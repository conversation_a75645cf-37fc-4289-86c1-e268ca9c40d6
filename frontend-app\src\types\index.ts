// 重新导出API类型
export * from '@/api/auth'
export * from '@/api/knowledgeBase'
export * from '@/api/document'
export * from '@/api/chat'
export * from '@/api/aiModel'
export * from '@/api/stats'
export * from '@/api/admin'

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  display_name: string
  avatar_url?: string
  bio?: string
  is_admin: boolean
  status: 'active' | 'disabled' | 'pending'
  last_login_at?: string
  created_at: string
  updated_at: string
}

export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

// 知识库相关类型
export interface KnowledgeBase {
  id: number
  owner_id: number
  name: string
  description?: string
  created_at: string
  updated_at: string
}

export interface CreateKnowledgeBaseForm {
  name: string
  description?: string
}

// 文档相关类型
export interface Document {
  id: number
  filename: string
  originalName: string
  size: number
  status: 'processing' | 'success' | 'failed'
  uploadDate: string
  knowledgeBaseId: number
}

export interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
}

// 聊天相关类型
export interface ChatSession {
  id: number
  title: string
  model: string
  createdAt: string
  lastMessageAt: string
  knowledgeBases: number[]
}

export interface ChatMessage {
  id: number
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  sources?: DocumentSource[]
}

export interface DocumentSource {
  documentId: number
  documentName: string
  relevance: number
  excerpt: string
}

export interface CreateChatForm {
  title?: string
  model: string
  knowledgeBases: number[]
  contextLength: number
}

// AI供应商相关类型
export interface AIProvider {
  id: number
  name: string
  display_name: string
  base_url?: string
  is_active: boolean
  description?: string
  created_at: string
  updated_at: string
}

// AI模型相关类型
export interface AIModel {
  id: number
  provider_id: number
  model_name: string
  display_name: string
  is_active: boolean
  system_api_key?: string
  allow_system_key_use: boolean
  max_tokens?: number
  supports_streaming: boolean
  cost_per_1k_tokens?: number
  provider?: AIProvider
  created_at: string
  updated_at: string
}

export interface UserAPIKey {
  id: number
  provider_id: number
  provider?: AIProvider
  description?: string
  created_at: string
  updated_at: string
}

// 设置相关类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'system'
  fontSize: 'small' | 'medium' | 'large'
  defaultModel: number | null
  defaultKnowledgeBases: number[]
  chatRetentionDays: number
  enableHighContrast: boolean
  enableReducedMotion: boolean
  providerPreferences?: Record<string, any>
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 统计数据类型
export interface UserStats {
  knowledgeBaseCount: number
  documentCount: number
  chatSessionCount: number
  storageUsed: number
  storageLimit: number
  unreadNotifications?: number
  pendingTasks?: number
}

// 额外的UI相关类型
export interface UploadFile {
  file: File
  id: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
}

export interface DocumentSource {
  documentId: number
  documentName: string
  relevance: number
  excerpt: string
}

// 日志相关类型
export interface SystemLog {
  id: number
  type: 'admin_action' | 'user_activity' | 'system_error'
  operator: string
  ipAddress: string
  action: string
  details: string
  timestamp: string
}
