<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-result {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error {
            background: #f8e8e8;
            border: 1px solid #e6c3c3;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>图表解析功能测试</h1>
    
    <div class="test-section">
        <h2>测试1: ECharts Option 格式</h2>
        <div class="test-input" id="test1-input">option = {
  title: { text: '销售数据统计' },
  xAxis: { data: ['1月', '2月', '3月', '4月'] },
  yAxis: {},
  series: [{
    name: '销售额',
    type: 'bar',
    data: [120, 200, 150, 80]
  }]
}</div>
        <button onclick="testChart(1)">测试解析</button>
        <div class="test-result" id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>测试2: 简单对象格式</h2>
        <div class="test-input" id="test2-input">{
  title: { text: '用户增长' },
  series: [{
    type: 'line',
    data: [10, 20, 30, 40]
  }],
  xAxis: { data: ['周一', '周二', '周三', '周四'] }
}</div>
        <button onclick="testChart(2)">测试解析</button>
        <div class="test-result" id="test2-result"></div>
    </div>

    <div class="test-section">
        <h2>测试3: Chart.js 格式</h2>
        <div class="test-input" id="test3-input">{
  "type": "pie",
  "title": "市场份额",
  "data": {
    "labels": ["产品A", "产品B", "产品C"],
    "datasets": [{
      "data": [30, 40, 30]
    }]
  }
}</div>
        <button onclick="testChart(3)">测试解析</button>
        <div class="test-result" id="test3-result"></div>
    </div>

    <script>
        // 模拟前端的图表解析函数
        function isEChartsOption(obj) {
            if (!obj || typeof obj !== 'object') return false;
            const echartsKeys = ['series', 'xAxis', 'yAxis', 'legend', 'tooltip', 'grid', 'title'];
            return echartsKeys.some(key => obj.hasOwnProperty(key));
        }

        function parseEChartsOption(code) {
            try {
                console.log('尝试解析ECharts代码:', code);
                
                let cleanCode = code.trim();
                let optionStr = '';
                
                // 模式1: option = { ... }
                let optionMatch = cleanCode.match(/option\s*=\s*({[\s\S]*})/);
                if (optionMatch) {
                    optionStr = optionMatch[1];
                } 
                // 模式2: 直接是对象 { ... }
                else if (cleanCode.startsWith('{') && cleanCode.endsWith('}')) {
                    optionStr = cleanCode;
                }
                
                if (!optionStr) {
                    console.log('未找到有效的option对象');
                    return null;
                }
                
                console.log('提取的option字符串:', optionStr);
                
                // 尝试解析为JavaScript对象
                let option = new Function('return ' + optionStr)();
                console.log('解析后的option对象:', option);
                
                return convertEChartsToChartJS(option);
            } catch (error) {
                console.error('ECharts解析错误:', error);
                return null;
            }
        }

        function convertEChartsToChartJS(echartsOption) {
            try {
                console.log('开始转换ECharts配置:', echartsOption);
                
                if (!echartsOption.series || !Array.isArray(echartsOption.series)) {
                    console.log('没有找到series数组');
                    return null;
                }

                const firstSeries = echartsOption.series[0];
                const chartType = mapEChartsTypeToChartJS(firstSeries.type);
                
                console.log('图表类型:', chartType, '原始类型:', firstSeries.type);
                
                const title = echartsOption.title?.text || '';
                
                let labels = [];
                let datasets = [];
                
                if (firstSeries.type === 'pie') {
                    labels = firstSeries.data?.map((item) => item.name || '') || [];
                    datasets = [{
                        label: firstSeries.name || '数据',
                        data: firstSeries.data?.map((item) => item.value || 0) || [],
                        backgroundColor: generateColors(labels.length)
                    }];
                } else {
                    if (echartsOption.xAxis) {
                        if (Array.isArray(echartsOption.xAxis)) {
                            labels = echartsOption.xAxis[0]?.data || [];
                        } else {
                            labels = echartsOption.xAxis.data || [];
                        }
                    } else if (firstSeries.data && Array.isArray(firstSeries.data)) {
                        labels = firstSeries.data.map((_, index) => `项目${index + 1}`);
                    }
                    
                    console.log('提取的标签:', labels);
                    
                    datasets = echartsOption.series.map((series, index) => {
                        const seriesData = Array.isArray(series.data) ? series.data : [];
                        console.log(`系列${index}数据:`, seriesData);
                        
                        return {
                            label: series.name || `系列${index + 1}`,
                            data: seriesData,
                            backgroundColor: series.itemStyle?.color || generateColors(1)[0],
                            borderColor: series.itemStyle?.borderColor || series.itemStyle?.color || generateColors(1)[0],
                            borderWidth: 2,
                            fill: chartType === 'line' ? false : true
                        };
                    });
                }

                console.log('转换后的数据集:', datasets);

                const result = {
                    type: chartType,
                    title,
                    data: {
                        labels,
                        datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            title: {
                                display: !!title,
                                text: title
                            }
                        },
                        scales: chartType !== 'pie' ? {
                            y: {
                                beginAtZero: true
                            }
                        } : undefined
                    }
                };
                
                console.log('最终转换结果:', result);
                return result;
            } catch (error) {
                console.error('ECharts转换错误:', error);
                return null;
            }
        }

        function mapEChartsTypeToChartJS(echartsType) {
            const typeMap = {
                'line': 'line',
                'bar': 'bar',
                'pie': 'pie',
                'scatter': 'scatter',
                'radar': 'radar',
                'doughnut': 'doughnut'
            };
            
            return typeMap[echartsType] || 'bar';
        }

        function generateColors(count) {
            const colors = [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
            ];
            
            const result = [];
            for (let i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
        }

        function parseChartCode(code) {
            try {
                let cleanCode = code.trim();
                
                // 优先检查是否为ECharts配置
                if (cleanCode.includes('option') || cleanCode.includes('series') || cleanCode.includes('xAxis') || cleanCode.includes('yAxis') || cleanCode.includes('title')) {
                    const echartsResult = parseEChartsOption(cleanCode);
                    if (echartsResult) {
                        return echartsResult;
                    }
                }

                // 尝试解析为JSON
                if (cleanCode.startsWith('{') && cleanCode.endsWith('}')) {
                    try {
                        const parsed = JSON.parse(cleanCode);
                        
                        // 检查是否为ECharts配置
                        if (isEChartsOption(parsed)) {
                            return convertEChartsToChartJS(parsed);
                        }
                        
                        return parsed; // 假设是Chart.js格式
                    } catch {
                        return parseEChartsOption(cleanCode);
                    }
                }

                return null;
            } catch (error) {
                console.error('图表解析错误:', error);
                return null;
            }
        }

        function testChart(testNumber) {
            const input = document.getElementById(`test${testNumber}-input`).textContent;
            const resultDiv = document.getElementById(`test${testNumber}-result`);
            
            try {
                const result = parseChartCode(input);
                
                if (result) {
                    resultDiv.className = 'test-result';
                    resultDiv.innerHTML = `
                        <h4>解析成功！</h4>
                        <p><strong>图表类型:</strong> ${result.type}</p>
                        <p><strong>标题:</strong> ${result.title || '无'}</p>
                        <p><strong>标签:</strong> ${JSON.stringify(result.data?.labels || [])}</p>
                        <p><strong>数据集:</strong> ${result.data?.datasets?.length || 0} 个</p>
                        <details>
                            <summary>完整结果</summary>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '<h4>解析失败</h4><p>无法识别图表格式</p>';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<h4>解析错误</h4><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
