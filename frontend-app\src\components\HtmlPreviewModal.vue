<template>
  <el-dialog
    v-model="visible"
    title="HTML 预览"
    width="95vw"
    :before-close="handleClose"
    class="html-preview-modal"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    top="2.5vh"
    :fullscreen="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :lock-scroll="true"
  >
    <template #header="{ close }">
      <div class="modal-header">
        <div class="header-left">
          <div class="traffic-lights">
            <div class="light red"></div>
            <div class="light yellow"></div>
            <div class="light green"></div>
          </div>
          <span class="modal-title">HTML 预览 - 放大视图</span>
        </div>
        <div class="header-right">
          <button
            @click="refreshPreview"
            class="action-btn"
            title="刷新"
          >
            <el-icon><Refresh /></el-icon>
          </button>
          <button
            @click="copyHtml"
            class="action-btn"
            title="复制HTML"
          >
            <el-icon><DocumentCopy /></el-icon>
          </button>
          <button
            @click="close"
            class="action-btn close-btn"
            title="关闭"
          >
            <el-icon><Close /></el-icon>
          </button>
        </div>
      </div>
    </template>

    <div class="modal-content">
      <div class="iframe-container">
        <iframe
          ref="previewFrame"
          :srcdoc="sanitizedHtml"
          class="preview-iframe"
          @load="onIframeLoad"
        ></iframe>
        <div v-if="loading" class="loading-overlay">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  DocumentCopy,
  Close,
  Loading
} from '@element-plus/icons-vue'
import DOMPurify from 'dompurify'

interface Props {
  modelValue: boolean
  htmlContent: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const previewFrame = ref<HTMLIFrameElement>()
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const sanitizedHtml = computed(() => {
  if (!props.htmlContent) return ''

  // 使用DOMPurify清理HTML，但保留必要的脚本标签用于图表
  let cleanHtml = DOMPurify.sanitize(props.htmlContent, {
    FORBID_ATTR: ['onload', 'onerror', 'onclick', 'onmouseover'],
    ALLOWED_TAGS: ['script', 'div', 'canvas', 'style', 'head', 'body', 'html', 'title', 'meta'],
    ALLOWED_ATTR: ['src', 'type', 'id', 'class', 'style', 'width', 'height']
  })

  // 如果HTML不包含完整的文档结构，自动包装并添加图表库
  if (!cleanHtml.includes('<!' + 'DOCTYPE html>') && !cleanHtml.includes('<' + 'html')) {
    const baseUrl = window.location.origin
    const parts = []
    parts.push('<!' + 'DOCTYPE html>')
    parts.push('<' + 'html lang="zh-CN">')
    parts.push('<' + 'head>')
    parts.push('    <' + 'meta charset="UTF-8">')
    parts.push('    <' + 'meta name="viewport" content="width=device-width, initial-scale=1.0">')
    parts.push('    <' + 'title>HTML预览</' + 'title>')
    parts.push('    <' + 'script src="' + baseUrl + '/libs/chart.js"></' + 'script>')
    parts.push('    <' + 'script src="' + baseUrl + '/libs/echarts.js"></' + 'script>')
    parts.push('    <' + 'style>')
    parts.push('        body {')
    parts.push('            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;')
    parts.push('            margin: 20px;')
    parts.push('            line-height: 1.6;')
    parts.push('        }')
    parts.push('        .chart-container {')
    parts.push('            width: 100%;')
    parts.push('            height: 500px;')
    parts.push('            margin: 20px 0;')
    parts.push('            position: relative;')
    parts.push('        }')
    parts.push('        canvas {')
    parts.push('            max-width: 100% !important;')
    parts.push('            height: auto !important;')
    parts.push('        }')
    parts.push('        /* 确保图表正确渲染 */')
    parts.push('        .chart-wrapper {')
    parts.push('            position: relative;')
    parts.push('            width: 100%;')
    parts.push('            height: 500px;')
    parts.push('        }')
    parts.push('        .chart-wrapper canvas {')
    parts.push('            position: absolute !important;')
    parts.push('            top: 0 !important;')
    parts.push('            left: 0 !important;')
    parts.push('        }')
    parts.push('    </' + 'style>')
    parts.push('    <' + 'script>')
    parts.push('        // 确保图表库加载完成后再执行图表代码')
    parts.push('        window.addEventListener("load", function() {')
    parts.push('            console.log("页面加载完成，Chart.js可用:", typeof Chart !== "undefined");')
    parts.push('            console.log("ECharts可用:", typeof echarts !== "undefined");')
    parts.push('            // 如果有图表代码，确保在这里执行')
    parts.push('            setTimeout(function() {')
    parts.push('                // 重新触发任何图表初始化代码')
    parts.push('                if (window.initCharts) {')
    parts.push('                    window.initCharts();')
    parts.push('                }')
    parts.push('            }, 100);')
    parts.push('        });')
    parts.push('    </' + 'script>')
    parts.push('</' + 'head>')
    parts.push('<' + 'body>')
    parts.push('    ' + cleanHtml)
    parts.push('</' + 'body>')
    parts.push('</' + 'html>')
    cleanHtml = parts.join('\n')
  } else {
    // 如果是完整HTML，确保包含图表库
    if (!cleanHtml.includes('chart.js') && !cleanHtml.includes('echarts')) {
      const baseUrl = window.location.origin
      const scriptParts = []
      scriptParts.push('    <' + 'script src="' + baseUrl + '/libs/chart.js"></' + 'script>')
      scriptParts.push('    <' + 'script src="' + baseUrl + '/libs/echarts.js"></' + 'script>')
      scriptParts.push('</' + 'head>')
      const scriptTags = scriptParts.join('\n')
      cleanHtml = cleanHtml.replace('</' + 'head>', scriptTags)
    }
  }

  return cleanHtml
})

const handleClose = () => {
  visible.value = false
}

const refreshPreview = async () => {
  if (!previewFrame.value) return
  
  loading.value = true
  try {
    // 重新设置srcdoc来刷新iframe
    const currentSrc = previewFrame.value.srcdoc
    previewFrame.value.srcdoc = ''
    await nextTick()
    previewFrame.value.srcdoc = currentSrc
  } catch (error) {
    console.error('刷新预览失败:', error)
    ElMessage.error('刷新预览失败')
  }
}

const copyHtml = async () => {
  try {
    await navigator.clipboard.writeText(props.htmlContent)
    ElMessage.success('HTML代码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

const onIframeLoad = () => {
  loading.value = false
}

// 监听htmlContent变化，自动刷新
watch(() => props.htmlContent, () => {
  if (visible.value) {
    loading.value = true
  }
}, { immediate: true })
</script>

<style scoped>
.html-preview-modal :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  margin: 0 !important;
  width: 95vw !important;
  height: 95vh !important;
  max-width: none !important;
  max-height: none !important;
  display: flex !important;
  flex-direction: column !important;
}

.html-preview-modal :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
  flex-shrink: 0;
  height: auto;
  min-height: 0;
}

.html-preview-modal :deep(.el-dialog__body) {
  padding: 0 !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  min-height: 0 !important;
  height: calc(95vh - 60px) !important;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e1e5e9;
  flex-shrink: 0;
  min-height: 48px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.traffic-lights {
  display: flex;
  gap: 6px;
}

.light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.light.red {
  background: #ff5f57;
}

.light.yellow {
  background: #ffbd2e;
}

.light.green {
  background: #28ca42;
}

.modal-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.header-right {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  transform: translateY(-1px);
}

.close-btn:hover {
  background: #ff5f57;
  color: white;
}

.modal-content {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  min-height: 0 !important;
}

.iframe-container {
  flex: 1 !important;
  position: relative !important;
  background: white !important;
  min-height: 0 !important; /* 确保flex子项可以收缩 */
  overflow: hidden !important;
  height: calc(95vh - 108px) !important; /* 减去header高度 */
}

.preview-iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  background: white !important;
  display: block !important;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
}

.loading-icon {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 确保模态框不受任何外部容器限制 */
.html-preview-modal :deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 400vh !important;
  z-index: 9999 !important;
}

.html-preview-modal :deep(.el-overlay-dialog) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 400vh !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 强制设置dialog的高度 */
.html-preview-modal :deep(.el-dialog__wrapper) {
  height: 400vh !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
</style>
