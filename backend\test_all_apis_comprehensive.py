"""
全面测试所有API端点
验证API文档的准确性和完整性
"""
import requests
import tempfile
import os
import json
import time

BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self):
        self.token = None
        self.headers = {}
        self.test_data = {}
        
    def login(self):
        """登录获取token"""
        print("🔐 正在登录...")
        login_data = {"username": "testuser", "password": "test123"}
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.text}")
            return False
        
        self.token = response.json().get("access_token")
        self.headers = {"Authorization": f"Bearer {self.token}"}
        print("✅ 登录成功")
        return True
    
    def test_auth_apis(self):
        """测试认证API"""
        print("\n🔑 测试认证API...")
        
        # 测试获取当前用户信息
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/auth/me - 获取当前用户信息")
        else:
            print(f"❌ GET /api/auth/me - 失败: {response.status_code}")
    
    def test_knowledge_base_apis(self):
        """测试知识库API"""
        print("\n📚 测试知识库API...")
        
        # 获取知识库列表
        response = requests.get(f"{BASE_URL}/api/knowledge-bases/", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/knowledge-bases/ - 获取知识库列表")
        else:
            print(f"❌ GET /api/knowledge-bases/ - 失败: {response.status_code}")
        
        # 创建知识库
        kb_data = {"name": "API测试知识库", "description": "用于API测试"}
        response = requests.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=self.headers)
        
        if response.status_code in [200, 201]:
            kb_id = response.json().get("id")
            self.test_data["kb_id"] = kb_id
            print(f"✅ POST /api/knowledge-bases/ - 创建知识库 (ID: {kb_id})")
            
            # 获取知识库详情
            response = requests.get(f"{BASE_URL}/api/knowledge-bases/{kb_id}", headers=self.headers)
            if response.status_code == 200:
                print("✅ GET /api/knowledge-bases/{kb_id} - 获取知识库详情")
            else:
                print(f"❌ GET /api/knowledge-bases/{kb_id} - 失败: {response.status_code}")
            
            # 更新知识库
            update_data = {"description": "更新后的描述"}
            response = requests.put(f"{BASE_URL}/api/knowledge-bases/{kb_id}", json=update_data, headers=self.headers)
            if response.status_code == 200:
                print("✅ PUT /api/knowledge-bases/{kb_id} - 更新知识库")
            else:
                print(f"❌ PUT /api/knowledge-bases/{kb_id} - 失败: {response.status_code}")
        else:
            print(f"❌ POST /api/knowledge-bases/ - 失败: {response.status_code}")
    
    def test_document_apis(self):
        """测试文档API"""
        print("\n📄 测试文档API...")
        
        kb_id = self.test_data.get("kb_id")
        if not kb_id:
            print("❌ 没有可用的知识库ID，跳过文档API测试")
            return
        
        # 获取文档列表
        response = requests.get(f"{BASE_URL}/api/documents/kb/{kb_id}", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/documents/kb/{kb_id} - 获取文档列表")
        else:
            print(f"❌ GET /api/documents/kb/{kb_id} - 失败: {response.status_code}")
        
        # 创建测试文档
        test_content = "这是API测试文档内容。包含一些测试数据用于验证文档处理功能。"
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='_api_test.txt', delete=False, encoding='utf-8')
        temp_file.write(test_content)
        temp_file.close()
        
        try:
            # 上传文档
            with open(temp_file.name, 'rb') as f:
                files = {'file': (os.path.basename(temp_file.name), f, 'text/plain')}
                response = requests.post(f"{BASE_URL}/api/documents/upload/{kb_id}", files=files, headers=self.headers)
            
            if response.status_code == 200:
                doc_data = response.json()
                doc_id = doc_data.get("id")
                self.test_data["doc_id"] = doc_id
                print(f"✅ POST /api/documents/upload/{kb_id} - 上传文档 (ID: {doc_id})")
                
                # 获取文档详情
                response = requests.get(f"{BASE_URL}/api/documents/{doc_id}", headers=self.headers)
                if response.status_code == 200:
                    print("✅ GET /api/documents/{doc_id} - 获取文档详情")
                else:
                    print(f"❌ GET /api/documents/{doc_id} - 失败: {response.status_code}")
                
                # 测试文档向量化
                response = requests.post(f"{BASE_URL}/api/documents/{doc_id}/vectorize", headers=self.headers)
                if response.status_code == 200:
                    print("✅ POST /api/documents/{doc_id}/vectorize - 文档向量化")
                else:
                    print(f"❌ POST /api/documents/{doc_id}/vectorize - 失败: {response.status_code}")
            else:
                print(f"❌ POST /api/documents/upload/{kb_id} - 失败: {response.status_code}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
        
        # 测试批量上传
        temp_files = []
        for i in range(2):
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=f'_batch_test_{i}.txt', delete=False, encoding='utf-8')
            temp_file.write(f"批量测试文档{i+1}的内容")
            temp_file.close()
            temp_files.append(temp_file.name)
        
        try:
            files = []
            for temp_file in temp_files:
                files.append(('files', (os.path.basename(temp_file), open(temp_file, 'rb'), 'text/plain')))
            
            response = requests.post(f"{BASE_URL}/api/documents/batch-upload/{kb_id}", files=files, headers=self.headers)
            
            # 关闭文件
            for _, (_, file_obj, _) in files:
                file_obj.close()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ POST /api/documents/batch-upload/{kb_id} - 批量上传文档 ({len(result['uploaded_documents'])}个)")
                
                # 保存文档ID用于批量删除测试
                if 'uploaded_documents' in result and result['uploaded_documents']:
                    doc_ids = []
                    for doc in result['uploaded_documents']:
                        if isinstance(doc, dict) and 'id' in doc:
                            doc_ids.append(doc['id'])
                        elif hasattr(doc, 'id'):
                            doc_ids.append(doc.id)

                    if doc_ids:
                        # 测试批量删除
                        delete_data = {"document_ids": doc_ids}
                        response = requests.delete(f"{BASE_URL}/api/documents/batch-delete", json=delete_data, headers=self.headers)
                        if response.status_code == 200:
                            print("✅ DELETE /api/documents/batch-delete - 批量删除文档")
                        else:
                            print(f"❌ DELETE /api/documents/batch-delete - 失败: {response.status_code}")
                    else:
                        print("⚠️ 无法获取文档ID，跳过批量删除测试")
            else:
                print(f"❌ POST /api/documents/batch-upload/{kb_id} - 失败: {response.status_code}")
        
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
    
    def test_chat_apis(self):
        """测试聊天API"""
        print("\n💬 测试聊天API...")
        
        # 获取聊天会话列表
        response = requests.get(f"{BASE_URL}/api/chat/sessions", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/chat/sessions - 获取聊天会话列表")
        else:
            print(f"❌ GET /api/chat/sessions - 失败: {response.status_code}")
        
        # 创建聊天会话
        session_data = {"title": "API测试聊天"}
        response = requests.post(f"{BASE_URL}/api/chat/sessions", json=session_data, headers=self.headers)
        
        if response.status_code in [200, 201]:
            session_id = response.json().get("id")
            self.test_data["session_id"] = session_id
            print(f"✅ POST /api/chat/sessions - 创建聊天会话 (ID: {session_id})")
            
            # 获取消息列表
            response = requests.get(f"{BASE_URL}/api/chat/sessions/{session_id}/messages", headers=self.headers)
            if response.status_code == 200:
                print("✅ GET /api/chat/sessions/{session_id}/messages - 获取消息列表")
            else:
                print(f"❌ GET /api/chat/sessions/{session_id}/messages - 失败: {response.status_code}")
            
            # 获取聊天历史
            response = requests.get(f"{BASE_URL}/api/chat/sessions/{session_id}/messages/history?limit=10", headers=self.headers)
            if response.status_code == 200:
                print("✅ GET /api/chat/sessions/{session_id}/messages/history - 获取聊天历史")
            else:
                print(f"❌ GET /api/chat/sessions/{session_id}/messages/history - 失败: {response.status_code}")
            
            # 流式聊天
            kb_id = self.test_data.get("kb_id")
            chat_data = {
                "message": "这是API测试消息",
                "model_id": "Qwen/Qwen2.5-7B-Instruct",
                "knowledge_base_ids": [kb_id] if kb_id else [],
                "history_limit": 5
            }
            
            response = requests.post(f"{BASE_URL}/api/chat/sessions/{session_id}/stream", 
                                   json=chat_data, headers=self.headers, stream=True)
            
            if response.status_code == 200:
                print("✅ POST /api/chat/sessions/{session_id}/stream - 流式聊天")
                # 简单读取响应
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                if data.get('type') == 'done':
                                    break
                            except json.JSONDecodeError:
                                continue
            else:
                print(f"❌ POST /api/chat/sessions/{session_id}/stream - 失败: {response.status_code}")
            
            # 清空会话消息
            response = requests.delete(f"{BASE_URL}/api/chat/sessions/{session_id}/messages", headers=self.headers)
            if response.status_code == 200:
                print("✅ DELETE /api/chat/sessions/{session_id}/messages - 清空会话消息")
            else:
                print(f"❌ DELETE /api/chat/sessions/{session_id}/messages - 失败: {response.status_code}")
        else:
            print(f"❌ POST /api/chat/sessions - 失败: {response.status_code}")
    
    def test_ai_model_apis(self):
        """测试AI模型API"""
        print("\n🤖 测试AI模型API...")
        
        # 获取AI模型列表
        response = requests.get(f"{BASE_URL}/api/ai/models")
        if response.status_code == 200:
            models = response.json()
            print(f"✅ GET /api/ai/models - 获取AI模型列表 ({len(models)}个模型)")
        else:
            print(f"❌ GET /api/ai/models - 失败: {response.status_code}")
        
        # 获取用户API密钥
        response = requests.get(f"{BASE_URL}/api/ai/api-keys", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/ai/api-keys - 获取用户API密钥")
        else:
            print(f"❌ GET /api/ai/api-keys - 失败: {response.status_code}")
    
    def test_stats_apis(self):
        """测试统计API"""
        print("\n📊 测试统计API...")
        
        # 获取基础统计
        response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=self.headers)
        if response.status_code == 200:
            stats = response.json()
            print("✅ GET /api/stats/dashboard - 获取基础统计")
            print(f"   知识库: {stats['knowledge_bases']}, 文档: {stats['documents']}, 会话: {stats['chat_sessions']}")
        else:
            print(f"❌ GET /api/stats/dashboard - 失败: {response.status_code}")
        
        # 获取详细统计
        response = requests.get(f"{BASE_URL}/api/stats/detailed", headers=self.headers)
        if response.status_code == 200:
            print("✅ GET /api/stats/detailed - 获取详细统计")
        else:
            print(f"❌ GET /api/stats/detailed - 失败: {response.status_code}")
        
        # 获取活动统计
        response = requests.get(f"{BASE_URL}/api/stats/activity?days=7", headers=self.headers)
        if response.status_code == 200:
            activity = response.json()
            print(f"✅ GET /api/stats/activity - 获取活动统计 ({len(activity)}天)")
        else:
            print(f"❌ GET /api/stats/activity - 失败: {response.status_code}")
    
    def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        # 删除聊天会话
        session_id = self.test_data.get("session_id")
        if session_id:
            response = requests.delete(f"{BASE_URL}/api/chat/sessions/{session_id}", headers=self.headers)
            if response.status_code == 200:
                print("✅ 删除测试聊天会话")
        
        # 删除文档
        doc_id = self.test_data.get("doc_id")
        if doc_id:
            response = requests.delete(f"{BASE_URL}/api/documents/{doc_id}", headers=self.headers)
            if response.status_code == 200:
                print("✅ 删除测试文档")
        
        # 删除知识库
        kb_id = self.test_data.get("kb_id")
        if kb_id:
            response = requests.delete(f"{BASE_URL}/api/knowledge-bases/{kb_id}", headers=self.headers)
            if response.status_code == 200:
                print("✅ 删除测试知识库")
    
    def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始全面API测试...")
        
        if not self.login():
            return
        
        try:
            self.test_auth_apis()
            self.test_knowledge_base_apis()
            self.test_document_apis()
            self.test_chat_apis()
            self.test_ai_model_apis()
            self.test_stats_apis()
        finally:
            self.cleanup()
        
        print("\n🎉 API测试完成！")

def main():
    tester = APITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
