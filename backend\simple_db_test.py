"""
简单的数据库连接测试
"""
import asyncio
import asyncpg
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_connection():
    """测试数据库连接"""
    try:
        # 数据库配置
        host = "localhost"
        port = 5432
        user = "postgres"
        password = "111222"
        database_name = "aiknowledgebase"
        
        print(f"正在连接到 PostgreSQL...")
        print(f"主机: {host}:{port}")
        print(f"用户: {user}")
        
        # 先连接到postgres数据库
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"
        )
        print("✓ 成功连接到PostgreSQL服务器")
        
        # 检查目标数据库是否存在
        result = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1",
            database_name
        )
        
        if result:
            print(f"✓ 数据库 '{database_name}' 已存在")
        else:
            print(f"✗ 数据库 '{database_name}' 不存在，正在创建...")
            await conn.execute(f'CREATE DATABASE "{database_name}"')
            print(f"✓ 数据库 '{database_name}' 创建成功")
        
        await conn.close()
        
        # 连接到目标数据库
        target_conn = await asyncpg.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database_name
        )
        print(f"✓ 成功连接到目标数据库 '{database_name}'")
        await target_conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查:")
        print("1. PostgreSQL服务是否正在运行")
        print("2. 用户名和密码是否正确")
        print("3. 端口是否正确")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    if success:
        print("\n✓ 数据库连接测试成功！")
    else:
        print("\n✗ 数据库连接测试失败！")
        sys.exit(1)
