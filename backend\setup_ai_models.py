#!/usr/bin/env python3
"""
设置AI模型数据
"""
import asyncio
from sqlmodel import Session, select
from app.core.database import get_session
from app.models.ai import AIProvider

async def setup_ai_models():
    """设置AI模型数据"""
    session = next(get_session())
    
    try:
        # 检查是否已有模型
        existing_models = session.exec(select(AIProvider)).all()
        print(f"现有模型数量: {len(existing_models)}")
        
        if existing_models:
            print("现有模型:")
            for model in existing_models:
                print(f"  - {model.provider_name}: {model.model_name} (活跃: {model.is_active})")
        
        # 检查是否有 qwen-plus 模型
        qwen_plus_exists = any(model.model_name == "qwen-plus" for model in existing_models)

        if not qwen_plus_exists:
            print("添加 qwen-plus 模型...")
            qwen_plus_model = AIProvider(
                provider_name="硅基流动",
                model_name="qwen-plus",
                is_active=True,
                allow_system_key_use=True,
                system_api_key="sk-test-key"
            )
            session.add(qwen_plus_model)
            session.commit()
            print("qwen-plus 模型添加完成!")

        # 如果没有模型，添加一些默认模型
        if not existing_models:
            print("添加默认AI模型...")
            
            default_models = [
                {
                    "provider_name": "siliconflow",
                    "model_name": "qwen-plus",
                    "is_active": True,
                    "allow_system_key_use": True,
                    "system_api_key": "sk-test-key"  # 测试用的假密钥
                },
                {
                    "provider_name": "siliconflow", 
                    "model_name": "Qwen/Qwen2.5-7B-Instruct",
                    "is_active": True,
                    "allow_system_key_use": True,
                    "system_api_key": "sk-test-key"
                },
                {
                    "provider_name": "openai",
                    "model_name": "gpt-3.5-turbo",
                    "is_active": True,
                    "allow_system_key_use": False,
                    "system_api_key": None
                },
                {
                    "provider_name": "openai",
                    "model_name": "gpt-4",
                    "is_active": True,
                    "allow_system_key_use": False,
                    "system_api_key": None
                }
            ]
            
            for model_data in default_models:
                model = AIProvider(**model_data)
                session.add(model)
                print(f"  添加模型: {model_data['provider_name']} - {model_data['model_name']}")
            
            session.commit()
            print("默认AI模型添加完成!")
        
        # 再次检查
        all_models = session.exec(select(AIProvider)).all()
        print(f"\n最终模型数量: {len(all_models)}")
        for model in all_models:
            print(f"  - ID: {model.id}, Provider: {model.provider_name}, Model: {model.model_name}, Active: {model.is_active}")
            
    except Exception as e:
        print(f"设置AI模型失败: {e}")
        session.rollback()
    finally:
        session.close()

if __name__ == "__main__":
    asyncio.run(setup_ai_models())
