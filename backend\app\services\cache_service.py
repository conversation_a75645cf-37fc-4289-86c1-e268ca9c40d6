"""
完整版缓存服务
使用Redis进行高效缓存，支持多种数据类型和TTL管理
"""
import logging
import json
import pickle
import hashlib
from typing import Any, Optional, Dict, List
import redis
import asyncio

logger = logging.getLogger(__name__)

class CacheServiceError(Exception):
    """缓存服务异常"""
    pass

class CacheService:
    """完整版缓存服务"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client = None
        self._memory_cache = {}  # 备用内存缓存
        
        # 默认TTL配置（秒）
        self.default_ttl = {
            "ai_response": 3600 * 24,      # AI响应缓存1天
            "chat_history": 3600 * 24 * 7, # 聊天历史缓存7天
            "document_chunks": 3600 * 24 * 30, # 文档分块缓存30天
            "embeddings": 3600 * 24 * 7,   # 嵌入向量缓存7天
            "user_session": 3600 * 2,      # 用户会话缓存2小时
            "api_rate_limit": 3600,        # API限流缓存1小时
        }
    
    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=False)
            await asyncio.get_event_loop().run_in_executor(None, self.redis_client.ping)
            logger.info("Redis缓存服务初始化成功")
        except Exception as e:
            logger.error(f"Redis缓存服务初始化失败: {e}")
            logger.warning("缓存服务将在内存模式下运行")
            self.redis_client = None
    
    async def close(self):
        """关闭连接"""
        try:
            if self.redis_client:
                await asyncio.get_event_loop().run_in_executor(None, self.redis_client.close)
                logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接失败: {e}")
    
    def _get_key(self, cache_type: str, key: str) -> str:
        """生成缓存键"""
        return f"cache:{cache_type}:{key}"
    
    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        try:
            if isinstance(value, (str, int, float, bool)):
                return json.dumps(value).encode('utf-8')
            else:
                return pickle.dumps(value)
        except Exception as e:
            logger.error(f"序列化失败: {e}")
            return pickle.dumps(value)
    
    def _deserialize_value(self, value: bytes) -> Any:
        """反序列化值"""
        try:
            # 先尝试JSON
            return json.loads(value.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            try:
                # 再尝试pickle
                return pickle.loads(value)
            except Exception as e:
                logger.error(f"反序列化失败: {e}")
                return None
    
    async def set(
        self, 
        cache_type: str, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存"""
        try:
            cache_key = self._get_key(cache_type, key)
            
            if self.redis_client:
                serialized_value = self._serialize_value(value)
                if ttl is None:
                    ttl = self.default_ttl.get(cache_type, 3600)
                
                await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.setex, cache_key, ttl, serialized_value
                )
            else:
                # 内存缓存模式
                self._memory_cache[cache_key] = {
                    'value': value,
                    'expires_at': asyncio.get_event_loop().time() + (ttl or 3600)
                }
            
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败 {cache_type}:{key}: {e}")
            return False
    
    async def get(self, cache_type: str, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            cache_key = self._get_key(cache_type, key)
            
            if self.redis_client:
                value = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.get, cache_key
                )
                if value is None:
                    return None
                return self._deserialize_value(value)
            else:
                # 内存缓存模式
                cached_item = self._memory_cache.get(cache_key)
                if cached_item:
                    if asyncio.get_event_loop().time() < cached_item['expires_at']:
                        return cached_item['value']
                    else:
                        # 过期删除
                        del self._memory_cache[cache_key]
                return None
            
        except Exception as e:
            logger.error(f"获取缓存失败 {cache_type}:{key}: {e}")
            return None
    
    async def delete(self, cache_type: str, key: str) -> bool:
        """删除缓存"""
        try:
            cache_key = self._get_key(cache_type, key)
            
            if self.redis_client:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.delete, cache_key
                )
            else:
                # 内存缓存模式
                self._memory_cache.pop(cache_key, None)
            
            return True
            
        except Exception as e:
            logger.error(f"删除缓存失败 {cache_type}:{key}: {e}")
            return False
    
    async def exists(self, cache_type: str, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            cache_key = self._get_key(cache_type, key)
            
            if self.redis_client:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.exists, cache_key
                )
                return bool(result)
            else:
                # 内存缓存模式
                cached_item = self._memory_cache.get(cache_key)
                if cached_item:
                    if asyncio.get_event_loop().time() < cached_item['expires_at']:
                        return True
                    else:
                        del self._memory_cache[cache_key]
                return False
            
        except Exception as e:
            logger.error(f"检查缓存存在失败 {cache_type}:{key}: {e}")
            return False
    
    # AI相关缓存方法
    async def cache_ai_response(self, prompt_hash: str, response: str, model: str = "default") -> bool:
        """缓存AI响应"""
        cache_key = f"{model}:{prompt_hash}"
        return await self.set("ai_response", cache_key, response)
    
    async def get_cached_ai_response(self, prompt_hash: str, model: str = "default") -> Optional[str]:
        """获取缓存的AI响应"""
        cache_key = f"{model}:{prompt_hash}"
        return await self.get("ai_response", cache_key)
    
    # 聊天历史缓存方法
    async def cache_chat_history(self, session_id: int, messages: List[Dict]) -> bool:
        """缓存聊天历史"""
        return await self.set("chat_history", str(session_id), messages)
    
    async def get_chat_history(self, session_id: int) -> List[Dict]:
        """获取聊天历史"""
        history = await self.get("chat_history", str(session_id))
        return history if history else []
    
    async def append_chat_message(self, session_id: int, message: Dict) -> bool:
        """追加聊天消息"""
        try:
            history = await self.get_chat_history(session_id)
            history.append(message)
            
            # 限制历史长度，只保留最近50条
            if len(history) > 50:
                history = history[-50:]
            
            return await self.cache_chat_history(session_id, history)
        except Exception as e:
            logger.error(f"追加聊天消息失败: {e}")
            return False
    
    # 文档分块缓存方法
    async def cache_document_chunks(self, document_id: int, chunks: List[Dict]) -> bool:
        """缓存文档分块"""
        return await self.set("document_chunks", str(document_id), chunks)
    
    async def get_document_chunks(self, document_id: int) -> Optional[List[Dict]]:
        """获取文档分块缓存"""
        return await self.get("document_chunks", str(document_id))
    
    # 嵌入向量缓存方法
    async def cache_embedding(self, text_hash: str, embedding: List[float], model: str = "default") -> bool:
        """缓存嵌入向量"""
        cache_key = f"{model}:{text_hash}"
        return await self.set("embeddings", cache_key, embedding)
    
    async def get_cached_embedding(self, text_hash: str, model: str = "default") -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        cache_key = f"{model}:{text_hash}"
        return await self.get("embeddings", cache_key)
    
    # 用户会话缓存方法
    async def cache_user_session(self, user_id: int, session_data: Dict) -> bool:
        """缓存用户会话"""
        return await self.set("user_session", str(user_id), session_data)
    
    async def get_user_session(self, user_id: int) -> Optional[Dict]:
        """获取用户会话"""
        return await self.get("user_session", str(user_id))
    
    # API限流缓存方法
    async def increment_api_usage(self, user_id: int, api_type: str = "general") -> int:
        """增加API使用计数"""
        try:
            cache_key = f"{user_id}:{api_type}"
            
            if self.redis_client:
                # 使用Redis的原子操作
                pipe = self.redis_client.pipeline()
                pipe.incr(self._get_key("api_rate_limit", cache_key))
                pipe.expire(self._get_key("api_rate_limit", cache_key), 3600)  # 1小时过期
                results = await asyncio.get_event_loop().run_in_executor(None, pipe.execute)
                return results[0]
            else:
                # 内存缓存模式
                current_count = await self.get("api_rate_limit", cache_key) or 0
                new_count = current_count + 1
                await self.set("api_rate_limit", cache_key, new_count, 3600)
                return new_count
                
        except Exception as e:
            logger.error(f"增加API使用计数失败: {e}")
            return 0
    
    async def get_api_usage(self, user_id: int, api_type: str = "general") -> int:
        """获取API使用计数"""
        cache_key = f"{user_id}:{api_type}"
        count = await self.get("api_rate_limit", cache_key)
        return count if count else 0
    
    # 批量操作方法
    async def mget(self, cache_type: str, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存"""
        try:
            result = {}
            
            if self.redis_client:
                cache_keys = [self._get_key(cache_type, key) for key in keys]
                values = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.mget, cache_keys
                )
                
                for key, value in zip(keys, values):
                    if value is not None:
                        result[key] = self._deserialize_value(value)
            else:
                # 内存缓存模式
                for key in keys:
                    value = await self.get(cache_type, key)
                    if value is not None:
                        result[key] = value
            
            return result
            
        except Exception as e:
            logger.error(f"批量获取缓存失败: {e}")
            return {}
    
    async def mset(self, cache_type: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存"""
        try:
            if self.redis_client:
                pipe = self.redis_client.pipeline()
                
                for key, value in data.items():
                    cache_key = self._get_key(cache_type, key)
                    serialized_value = self._serialize_value(value)
                    cache_ttl = ttl or self.default_ttl.get(cache_type, 3600)
                    pipe.setex(cache_key, cache_ttl, serialized_value)
                
                await asyncio.get_event_loop().run_in_executor(None, pipe.execute)
            else:
                # 内存缓存模式
                for key, value in data.items():
                    await self.set(cache_type, key, value, ttl)
            
            return True
            
        except Exception as e:
            logger.error(f"批量设置缓存失败: {e}")
            return False
    
    # 统计方法
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.redis_client:
                info = await asyncio.get_event_loop().run_in_executor(
                    None, self.redis_client.info
                )
                return {
                    "redis_version": info.get("redis_version"),
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "total_commands_processed": info.get("total_commands_processed"),
                    "keyspace_hits": info.get("keyspace_hits"),
                    "keyspace_misses": info.get("keyspace_misses"),
                }
            else:
                return {
                    "mode": "memory",
                    "cached_items": len(self._memory_cache),
                }
                
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}

# 全局缓存服务实例
cache_service = CacheService()

def get_cache_service() -> CacheService:
    """获取缓存服务实例"""
    return cache_service
