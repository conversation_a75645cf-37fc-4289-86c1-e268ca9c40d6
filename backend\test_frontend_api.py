#!/usr/bin/env python3
"""
测试前端API调用
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_frontend_api():
    """测试前端API调用"""
    session = requests.Session()
    
    # 1. 登录
    print("1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if login_response.status_code == 200:
        result = login_response.json()
        token = result["access_token"]
        session.headers.update({"Authorization": f"Bearer {token}"})
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    # 2. 测试获取供应商
    print("\n2. 测试获取供应商...")
    providers_response = session.get(f"{BASE_URL}/api/ai/providers")
    if providers_response.status_code == 200:
        providers = providers_response.json()
        print(f"✅ 获取供应商成功，共 {len(providers)} 个")
        for provider in providers[:3]:  # 只显示前3个
            print(f"   - {provider['display_name']} ({provider['name']})")
    else:
        print(f"❌ 获取供应商失败: {providers_response.text}")
    
    # 3. 测试获取模型
    print("\n3. 测试获取模型...")
    models_response = session.get(f"{BASE_URL}/api/ai/models")
    if models_response.status_code == 200:
        models = models_response.json()
        print(f"✅ 获取模型成功，共 {len(models)} 个")
        for model in models[:3]:  # 只显示前3个
            provider_name = model.get('provider', {}).get('display_name', '未知')
            print(f"   - {model['display_name']} ({provider_name})")
    else:
        print(f"❌ 获取模型失败: {models_response.text}")
    
    # 4. 测试获取用户信息
    print("\n4. 测试获取用户信息...")
    me_response = session.get(f"{BASE_URL}/api/auth/me")
    if me_response.status_code == 200:
        user_info = me_response.json()
        print(f"✅ 获取用户信息成功: {user_info['username']} (ID: {user_info['id']})")
    else:
        print(f"❌ 获取用户信息失败: {me_response.text}")
    
    print("\n🎉 前端API测试完成!")

if __name__ == "__main__":
    test_frontend_api()
