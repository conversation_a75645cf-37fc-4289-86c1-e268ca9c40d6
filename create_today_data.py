#!/usr/bin/env python3
import requests
from datetime import datetime

# 用管理员身份创建一些今日数据
login_data = {'username': 'admin', 'password': 'admin123'}
response = requests.post('http://localhost:8000/api/auth/login', json=login_data)

if response.status_code == 200:
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    print("🔧 创建今日测试数据...")
    
    # 1. 创建知识库
    kb_data = {
        "name": f"今日测试知识库_{datetime.now().strftime('%H%M%S')}",
        "description": "用于测试今日统计的知识库"
    }
    response = requests.post('http://localhost:8000/api/knowledge-bases', json=kb_data, headers=headers)
    if response.status_code == 201:
        kb = response.json()
        print(f"✅ 创建知识库: {kb['name']}")
        kb_id = kb['id']
        
        # 2. 上传文档到知识库
        # 创建一个简单的文本文件
        test_content = "这是一个测试文档，用于验证今日统计功能。"
        files = {
            'file': ('test_doc.txt', test_content, 'text/plain')
        }
        data = {
            'kb_id': kb_id
        }
        
        response = requests.post('http://localhost:8000/api/documents/upload', 
                               files=files, data=data, headers=headers)
        if response.status_code == 201:
            doc = response.json()
            print(f"✅ 上传文档: {doc['filename']}")
        else:
            print(f"❌ 上传文档失败: {response.status_code} - {response.text}")
    else:
        print(f"❌ 创建知识库失败: {response.status_code} - {response.text}")
    
    # 3. 创建聊天会话
    chat_data = {
        "title": f"今日测试对话_{datetime.now().strftime('%H%M%S')}"
    }
    response = requests.post('http://localhost:8000/api/chat/sessions', json=chat_data, headers=headers)
    if response.status_code == 201:
        session = response.json()
        print(f"✅ 创建聊天会话: {session['title']}")
    else:
        print(f"❌ 创建聊天会话失败: {response.status_code} - {response.text}")
    
    print("\n📊 检查今日活动统计...")
    
    # 4. 获取活动统计
    response = requests.get('http://localhost:8000/api/stats/activity?days=1', headers=headers)
    if response.status_code == 200:
        activity_stats = response.json()
        if activity_stats:
            today_stats = activity_stats[0]
            print(f"✅ 今日统计:")
            print(f"   日期: {today_stats['date']}")
            print(f"   知识库创建: {today_stats['knowledge_bases_created']}")
            print(f"   文档上传: {today_stats['documents_uploaded']}")
            print(f"   会话创建: {today_stats['chat_sessions_created']}")
            print(f"   消息发送: {today_stats['messages_sent']}")
        else:
            print("❌ 没有今日统计数据")
    else:
        print(f"❌ 获取活动统计失败: {response.status_code} - {response.text}")

else:
    print('❌ 管理员登录失败')
