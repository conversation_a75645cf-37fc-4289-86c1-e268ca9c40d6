#!/usr/bin/env python3
"""
测试基本API功能
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_basic_api():
    """测试基本API功能"""
    session = requests.Session()
    
    # 1. 登录
    print("1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if login_response.status_code == 200:
        result = login_response.json()
        token = result["access_token"]
        session.headers.update({"Authorization": f"Bearer {token}"})
        print("✅ 登录成功")
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    # 2. 获取AI供应商列表
    print("\n2. 测试获取AI供应商列表...")
    try:
        providers_response = session.get(f"{BASE_URL}/api/ai/providers")
        if providers_response.status_code == 200:
            providers = providers_response.json()
            print(f"✅ 获取供应商成功，共 {len(providers)} 个")
            for provider in providers[:3]:  # 只显示前3个
                print(f"   - {provider['display_name']} (ID: {provider['id']})")
        else:
            print(f"❌ 获取供应商失败: {providers_response.text}")
    except Exception as e:
        print(f"❌ 获取供应商异常: {e}")
    
    # 3. 获取AI模型列表
    print("\n3. 测试获取AI模型列表...")
    try:
        models_response = session.get(f"{BASE_URL}/api/ai/models")
        if models_response.status_code == 200:
            models = models_response.json()
            print(f"✅ 获取模型成功，共 {len(models)} 个")
            for model in models[:3]:  # 只显示前3个
                provider_name = model.get('provider', {}).get('display_name', '未知')
                print(f"   - {model['display_name']} ({provider_name})")
        else:
            print(f"❌ 获取模型失败: {models_response.text}")
    except Exception as e:
        print(f"❌ 获取模型异常: {e}")
    
    # 4. 获取用户API密钥
    print("\n4. 测试获取用户API密钥...")
    try:
        keys_response = session.get(f"{BASE_URL}/api/ai/api-keys")
        if keys_response.status_code == 200:
            user_keys = keys_response.json()
            print(f"✅ 获取用户密钥成功，共 {len(user_keys)} 个")
            for key in user_keys:
                provider_name = key.get('provider', {}).get('display_name', '未知')
                print(f"   - {provider_name}: {key.get('description', '无描述')}")
        else:
            print(f"❌ 获取用户密钥失败: {keys_response.text}")
    except Exception as e:
        print(f"❌ 获取用户密钥异常: {e}")
    
    print("\n🎉 基本API测试完成!")

if __name__ == "__main__":
    test_basic_api()
