# 数据库设计方案 (v2.0)

本文档基于对前端功能的全面分析，提供了一个重新设计的、更为健壮和可扩展的数据库结构。

## 核心设计理念

1.  **原子化与规范化**: 表结构遵循数据库设计范式，减少数据冗余，确保数据一致性。
2.  **功能驱动**: 每个表都直接或间接地服务于前端的一个或多个明确功能。
3.  **清晰的关系**: 表之间的关系明确，易于理解和查询。
4.  **统一的密钥管理**: 将AI密钥管理抽象为独立模块，以支持多种模型和灵活的配置。
5.  **日志与审计**: 强大的日志系统，用于追踪操作、监控系统状态和排查问题。


postgresql
用户名：postgres
密码：111222
主机地址：localhost
端口：5432
## 数据库表结构

### 1. 用户与认证 (User & Authentication)

#### `users` (用户表)
存储用户的核心信息、状态和角色。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 用户唯一ID |
| `username` | `VARCHAR(50) UNIQUE NOT NULL` | 用户名，用于登录 |
| `email` | `VARCHAR(100) UNIQUE NOT NULL` | 电子邮箱，用于登录和通知 |
| `password_hash` | `VARCHAR(255) NOT NULL` | 加密后的密码 |
| `display_name` | `VARCHAR(100)` | 显示名称（昵称） |
| `avatar_url` | `VARCHAR(255)` | 头像图片的URL |
| `bio` | `TEXT` | 个人简介 |
| `is_admin` | `BOOLEAN DEFAULT FALSE` | 是否为管理员 |
| `status` | `VARCHAR(20) DEFAULT 'active'`| 用户状态 (`active`, `disabled`, `pending`) |
| `last_login_at` | `TIMESTAMP` | 最后登录时间 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | 账户创建时间 |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | 账户更新时间 |

---

#### `sessions` (会话表)
管理用户的登录状态。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 会话ID |
| `user_id` | `INTEGER REFERENCES users(id)`| 关联的用户ID |
| `token` | `VARCHAR(255) UNIQUE NOT NULL`| 会话令牌 |
| `ip_address` | `VARCHAR(45)` | 登录时的IP地址 |
| `user_agent` | `TEXT` | 用户的浏览器信息 |
| `expires_at` | `TIMESTAMP NOT NULL` | 会话过期时间 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | 会话创建时间 |

### 2. 知识库核心 (Knowledge Base Core)

#### `knowledge_bases` (知识库表)
存储知识库的基本信息。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 知识库唯一ID |
| `owner_id` | `INTEGER REFERENCES users(id)` | 知识库拥有者的用户ID |
| `name` | `VARCHAR(100) NOT NULL` | 知识库名称 |
| `description` | `TEXT` | 知识库的详细描述 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | 创建时间 |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | 最后更新时间 |

---

#### `documents` (文档表)
存储上传至知识库的文档信息。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 文档唯一ID |
| `kb_id` | `INTEGER REFERENCES knowledge_bases(id)` | 所属知识库ID |
| `uploader_id` | `INTEGER REFERENCES users(id)` | 上传该文档的用户ID |
| `filename` | `VARCHAR(255) NOT NULL` | 原始文件名 |
| `storage_path` | `VARCHAR(255) NOT NULL` | 在服务器上的存储路径 |
| `file_type` | `VARCHAR(50)` | 文件类型 (e.g., `pdf`, `docx`) |
| `file_size` | `INTEGER` | 文件大小 (in bytes) |
| `status` | `VARCHAR(20) DEFAULT 'pending'`| 处理状态 (`pending`, `processing`, `completed`, `failed`) |
| `error_message` | `TEXT` | 如果处理失败，记录错误信息 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | 上传时间 |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | 状态更新时间 |

---

#### `document_chunks` (文档分块表)
存储文档被分割后的文本块，用于向量化和检索。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 分块唯一ID |
| `doc_id` | `INTEGER REFERENCES documents(id)`| 所属文档ID |
| `content` | `TEXT NOT NULL` | 分块的文本内容 |
| `vector` | `VECTOR(...)` | 文本内容的向量表示 (具体类型取决于向量数据库) |
| `metadata` | `JSONB` | 元数据 (e.g., 页码, 章节) |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | 创建时间 |

### 3. AI聊天与配置 (AI Chat & Configuration)

#### `ai_providers` (AI模型提供商表)
统一管理支持的AI模型及其提供商，便于扩展。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 模型唯一ID |
| `provider_name` | `VARCHAR(50) NOT NULL`| 提供商名称 (e.g., `OpenAI`, `Anthropic`, `SiliconFlow`) |
| `model_name` | `VARCHAR(100) NOT NULL` | 模型具体名称 (e.g., `gpt-4`, `claude-3-opus`) |
| `is_active` | `BOOLEAN DEFAULT TRUE` | 管理员控制该模型是否全局可用 |
| `system_api_key`| `VARCHAR(255)` | (加密存储) 管理员设置的系统通用API Key |
| `allow_system_key_use` | `BOOLEAN DEFAULT FALSE` | 管理员控制是否允许普通用户使用此系统Key |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | |

---

#### `user_api_keys` (用户个人密钥表)
存储用户为特定AI模型配置的个人密钥。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 唯一ID |
| `user_id` | `INTEGER REFERENCES users(id)`| 关联的用户ID |
| `model_id` | `INTEGER REFERENCES ai_providers(id)` | 关联的AI模型ID |
| `api_key` | `VARCHAR(255) NOT NULL` | (加密存储) 用户提供的个人API Key |
| `use_system_key` | `BOOLEAN DEFAULT FALSE` | 用户选择是否优先使用系统Key |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | |

---

#### `chat_sessions` (聊天会话表)
记录每一次独立的聊天过程。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 会话唯一ID |
| `user_id` | `INTEGER REFERENCES users(id)`| 参与会话的用户ID |
| `title` | `VARCHAR(255)` | 会话标题 (可由用户或AI生成) |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | 最后一次消息的时间 |

---

#### `chat_messages` (聊天消息表)
存储会话中的每一条消息。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 消息唯一ID |
| `session_id` | `INTEGER REFERENCES chat_sessions(id)`| 所属会话ID |
| `role` | `VARCHAR(20) NOT NULL` | 角色 (`user` 或 `assistant`) |
| `content` | `TEXT NOT NULL` | 消息的具体内容 |
| `model_id_used`|`INTEGER REFERENCES ai_providers(id)` | 本条回复使用的AI模型 |
| `referenced_kbs`|`JSONB` | 本条回复引用的知识库ID列表 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | |

### 4. 系统管理与日志 (System Administration & Logs)

#### `user_quotas` (用户配额表)
定义每个用户的资源使用限制。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `user_id` | `INTEGER PRIMARY KEY REFERENCES users(id)` | 用户ID |
| `max_kbs` | `INTEGER DEFAULT 5` | 最大知识库数量 |
| `max_docs_per_kb` | `INTEGER DEFAULT 100` | 单个知识库最大文档数 |
| `max_storage_mb` | `INTEGER DEFAULT 1024`| 最大存储空间 (MB) |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | |

---

#### `operation_logs` (操作日志表)
记录关键操作，用于审计和追踪。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id` | `SERIAL PRIMARY KEY` | 日志ID |
| `user_id` | `INTEGER REFERENCES users(id)` | 操作者ID (可为空，代表系统操作) |
| `action` | `VARCHAR(100) NOT NULL` | 操作类型 (e.g., `create_kb`, `delete_doc`, `disable_user`) |
| `target_type` | `VARCHAR(50)` | 操作对象的类型 (e.g., `knowledge_base`, `document`, `user`) |
| `target_id` | `INTEGER` | 操作对象的ID |
| `details` | `JSONB` | 操作详情 (e.g., 修改前后的值) |
| `ip_address` | `VARCHAR(45)` | 操作时的IP地址 |
| `created_at` | `TIMESTAMP DEFAULT NOW()` | |

---

#### `system_settings` (系统设置表)
存储全局配置项。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `key` | `VARCHAR(50) PRIMARY KEY`| 设置的唯一键 (e.g., `allow_registration`) |
| `value` | `TEXT` | 设置的值 |
| `description` | `TEXT` | 该设置的描述 |
| `updated_at` | `TIMESTAMP DEFAULT NOW()` | |

## 表关系图 (ER Diagram - Mermaid Syntax)

```mermaid
erDiagram
    users {
        SERIAL id PK
        VARCHAR(50) username
        VARCHAR(100) email
        VARCHAR(255) password_hash
        VARCHAR(100) display_name
        BOOLEAN is_admin
        VARCHAR(20) status
    }

    sessions {
        SERIAL id PK
        INTEGER user_id FK
        VARCHAR(255) token
        TIMESTAMP expires_at
    }

    knowledge_bases {
        SERIAL id PK
        INTEGER owner_id FK
        VARCHAR(100) name
    }

    documents {
        SERIAL id PK
        INTEGER kb_id FK
        INTEGER uploader_id FK
        VARCHAR(255) filename
        VARCHAR(20) status
    }

    document_chunks {
        SERIAL id PK
        INTEGER doc_id FK
        TEXT content
        VECTOR vector
    }

    ai_providers {
        SERIAL id PK
        VARCHAR(50) provider_name
        VARCHAR(100) model_name
        BOOLEAN is_active
        VARCHAR(255) system_api_key
        BOOLEAN allow_system_key_use
    }

    user_api_keys {
        SERIAL id PK
        INTEGER user_id FK
        INTEGER model_id FK
        VARCHAR(255) api_key
        BOOLEAN use_system_key
    }

    chat_sessions {
        SERIAL id PK
        INTEGER user_id FK
        VARCHAR(255) title
    }

    chat_messages {
        SERIAL id PK
        INTEGER session_id FK
        VARCHAR(20) role
        TEXT content
    }

    user_quotas {
        INTEGER user_id PK
        INTEGER max_kbs
        INTEGER max_storage_mb
    }
    
    operation_logs {
        SERIAL id PK
        INTEGER user_id FK
        VARCHAR(100) action
    }

    users ||--o{ sessions : "has"
    users ||--o{ knowledge_bases : "owns"
    users ||--o{ documents : "uploads"
    users ||--o{ chat_sessions : "has"
    users ||--o{ user_api_keys : "configures"
    users |o--o| user_quotas : "has"
    users ||--o{ operation_logs : "performs"
    
    knowledge_bases ||--o{ documents : "contains"
    documents ||--o{ document_chunks : "has"
    
    ai_providers ||--o{ user_api_keys : "is configured by"
    
    chat_sessions ||--o{ chat_messages : "has"
``` 