#!/usr/bin/env python3
"""
测试AI模型管理API
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

class AIAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_id = None

    def login_admin(self):
        """登录管理员账户"""
        print("正在登录管理员账户...")

        # 先尝试注册管理员账户（如果不存在）
        register_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "full_name": "管理员"
        }

        register_response = self.session.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if register_response.status_code == 200:
            print("管理员账户注册成功")
        elif register_response.status_code == 400:
            print("管理员账户已存在")
        else:
            print(f"注册失败: {register_response.text}")

        # 登录
        login_data = {
            "username": "admin",
            "password": "admin123"
        }

        login_response = self.session.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if login_response.status_code == 200:
            result = login_response.json()
            self.token = result["access_token"]
            self.session.headers.update({"Authorization": f"Bearer {self.token}"})
            print("登录成功!")

            # 获取用户信息
            me_response = self.session.get(f"{BASE_URL}/api/auth/me")
            if me_response.status_code == 200:
                user_info = me_response.json()
                self.user_id = user_info["id"]
                print(f"当前用户: {user_info['username']} (ID: {user_info['id']})")
                return True

        print(f"登录失败: {login_response.text}")
        return False

    def test_get_providers(self):
        """测试获取供应商列表"""
        print("\n" + "="*50)
        print("测试获取供应商列表...")
        response = self.session.get(f"{BASE_URL}/api/ai/providers")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            providers = response.json()
            print(f"找到 {len(providers)} 个供应商:")
            for provider in providers:
                print(f"  - {provider['display_name']} ({provider['name']}) - {'启用' if provider['is_active'] else '禁用'}")
            return providers
        else:
            print(f"错误: {response.text}")
            return []

    def test_get_models(self):
        """测试获取模型列表"""
        print("\n" + "="*50)
        print("测试获取模型列表...")
        response = self.session.get(f"{BASE_URL}/api/ai/models")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            models = response.json()
            print(f"找到 {len(models)} 个模型:")
            for model in models:
                provider_name = model.get('provider', {}).get('display_name', '未知')
                status = '启用' if model['is_active'] else '禁用'
                system_key = '有' if model.get('system_api_key') else '无'
                print(f"  - {model['display_name']} ({model['model_name']}) - {provider_name} - {status} - 系统密钥: {system_key}")
            return models
        else:
            print(f"错误: {response.text}")
            return []

    def test_create_provider(self):
        """测试创建供应商"""
        print("\n" + "="*50)
        print("测试创建新供应商...")

        new_provider = {
            "name": "test_provider",
            "display_name": "测试供应商",
            "base_url": "https://api.test.com/v1",
            "description": "这是一个测试供应商",
            "is_active": True
        }

        response = self.session.post(f"{BASE_URL}/api/ai/providers", json=new_provider)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            provider = response.json()
            print(f"供应商创建成功: {provider['display_name']} (ID: {provider['id']})")
            return provider
        else:
            print(f"创建失败: {response.text}")
            return None

    def test_create_model(self, provider_id):
        """测试创建模型"""
        print("\n" + "="*50)
        print("测试创建新模型...")

        new_model = {
            "provider_id": provider_id,
            "model_name": "test-model-v1",
            "display_name": "测试模型 V1",
            "system_api_key": "sk-test123456789",
            "is_active": True,
            "allow_system_key_use": True,
            "max_tokens": 4000,
            "supports_streaming": True,
            "cost_per_1k_tokens": 0.002
        }

        response = self.session.post(f"{BASE_URL}/api/ai/models", json=new_model)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            model = response.json()
            print(f"模型创建成功: {model['display_name']} (ID: {model['id']})")
            return model
        else:
            print(f"创建失败: {response.text}")
            return None

    def test_update_model(self, model_id):
        """测试更新模型"""
        print("\n" + "="*50)
        print("测试更新模型...")

        update_data = {
            "display_name": "测试模型 V1 (已更新)",
            "cost_per_1k_tokens": 0.003
        }

        response = self.session.put(f"{BASE_URL}/api/ai/models/{model_id}", json=update_data)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            model = response.json()
            print(f"模型更新成功: {model['display_name']}")
            return model
        else:
            print(f"更新失败: {response.text}")
            return None

    def test_user_api_keys(self, provider_id):
        """测试用户API密钥管理"""
        print("\n" + "="*50)
        print("测试用户API密钥管理...")

        # 创建用户API密钥
        key_data = {
            "provider_id": provider_id,
            "api_key": "sk-user123456789",
            "description": "我的测试密钥"
        }

        response = self.session.post(f"{BASE_URL}/api/ai/api-keys", json=key_data)
        print(f"创建密钥状态码: {response.status_code}")

        if response.status_code == 200:
            key = response.json()
            print(f"用户密钥创建成功: {key['description']} (ID: {key['id']})")

            # 获取用户密钥列表
            response = self.session.get(f"{BASE_URL}/api/ai/api-keys")
            if response.status_code == 200:
                keys = response.json()
                print(f"用户共有 {len(keys)} 个API密钥")

            return key
        else:
            print(f"创建密钥失败: {response.text}")
            return None

    def test_connection(self, provider_id, model_name):
        """测试连接"""
        print("\n" + "="*50)
        print("测试AI连接...")

        test_data = {
            "provider_id": provider_id,
            "model_name": model_name,
            "api_key": "sk-test123456789"
        }

        response = self.session.post(f"{BASE_URL}/api/ai/test-connection", json=test_data)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"连接测试结果: {'成功' if result['success'] else '失败'}")
            print(f"消息: {result['message']}")
            if result.get('response_time'):
                print(f"响应时间: {result['response_time']:.3f}秒")
        else:
            print(f"测试失败: {response.text}")

    def test_delete_resources(self, model_id, provider_id):
        """测试删除资源"""
        print("\n" + "="*50)
        print("测试删除资源...")

        # 删除模型
        response = self.session.delete(f"{BASE_URL}/api/ai/models/{model_id}")
        print(f"删除模型状态码: {response.status_code}")
        if response.status_code == 200:
            print("模型删除成功")

        # 删除供应商
        response = self.session.delete(f"{BASE_URL}/api/ai/providers/{provider_id}")
        print(f"删除供应商状态码: {response.status_code}")
        if response.status_code == 200:
            print("供应商删除成功")
        else:
            print(f"删除供应商失败: {response.text}")

    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试AI模型管理API...")
        print("=" * 60)

        # 登录
        if not self.login_admin():
            print("登录失败，无法继续测试")
            return

        # 基础查询测试
        providers = self.test_get_providers()
        models = self.test_get_models()

        # 创建测试
        new_provider = self.test_create_provider()
        if new_provider:
            provider_id = new_provider['id']

            # 创建模型
            new_model = self.test_create_model(provider_id)
            if new_model:
                model_id = new_model['id']

                # 更新模型
                self.test_update_model(model_id)

                # 用户密钥测试
                self.test_user_api_keys(provider_id)

                # 连接测试
                self.test_connection(provider_id, new_model['model_name'])

                # 清理测试数据
                self.test_delete_resources(model_id, provider_id)

        print("\n" + "="*60)
        print("所有测试完成!")

def main():
    """主函数"""
    tester = AIAPITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
