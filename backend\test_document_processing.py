"""
测试文档处理功能
"""
import asyncio
import aiohttp
import json
import tempfile
import os

BASE_URL = "http://localhost:8000"

async def test_document_processing():
    """测试文档处理功能"""
    
    async with aiohttp.ClientSession() as session:
        # 1. 登录
        login_data = {"username": "testuser", "password": "test123"}
        async with session.post(f"{BASE_URL}/api/auth/login", json=login_data) as response:
            if response.status != 200:
                print("❌ 登录失败")
                return
            
            result = await response.json()
            token = result.get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        
        # 2. 创建知识库
        kb_data = {"name": "文档处理测试知识库", "description": "测试文档处理功能"}
        async with session.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=headers) as response:
            if response.status not in [200, 201]:
                print("❌ 创建知识库失败")
                return
            
            result = await response.json()
            kb_id = result.get("id")
            print(f"✅ 创建知识库成功 - ID: {kb_id}")
        
        # 3. 创建测试文档
        test_content = """
        人工智能技术概述
        
        人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
        
        主要技术领域包括：
        1. 机器学习 - 使计算机能够在没有明确编程的情况下学习和改进
        2. 深度学习 - 使用神经网络来模拟人脑的工作方式
        3. 自然语言处理 - 使计算机能够理解和生成人类语言
        4. 计算机视觉 - 使计算机能够识别和理解图像
        
        应用场景：
        - 智能助手和聊天机器人
        - 自动驾驶汽车
        - 医疗诊断系统
        - 金融风险评估
        - 推荐系统
        
        未来发展趋势：
        随着技术的不断进步，人工智能将在更多领域发挥重要作用，
        包括教育、娱乐、制造业等。同时，我们也需要关注AI的伦理
        和安全问题，确保技术的发展能够造福人类。
        """
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            # 4. 上传文档
            with open(temp_file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='ai_overview.txt', content_type='text/plain')
                
                async with session.post(f"{BASE_URL}/api/documents/upload/{kb_id}", 
                                      data=data, headers=headers) as response:
                    if response.status not in [200, 201]:
                        text = await response.text()
                        print(f"❌ 文档上传失败 - 状态码: {response.status}, 响应: {text}")
                        return
                    
                    result = await response.json()
                    doc_id = result.get("id")
                    status = result.get("status")
                    print(f"✅ 文档上传成功 - ID: {doc_id}, 状态: {status}")
            
            # 5. 如果文档状态不是completed，尝试手动向量化
            if status != "completed":
                print("🔄 开始文档向量化...")
                async with session.post(f"{BASE_URL}/api/documents/{doc_id}/vectorize", 
                                      headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ 文档向量化成功")
                        print(f"✅ 处理状态: {result.get('status')}")
                        if 'chunks_count' in result:
                            print(f"✅ 生成分块数量: {result.get('chunks_count')}")
                    else:
                        text = await response.text()
                        print(f"❌ 文档向量化失败 - 状态码: {response.status}")
                        print(f"错误详情: {text}")
            
            # 6. 测试基于知识库的聊天
            print("\n💬 测试基于知识库的聊天:")
            
            # 创建聊天会话
            session_data = {"title": "文档处理测试聊天"}
            async with session.post(f"{BASE_URL}/api/chat/sessions", 
                                  json=session_data, headers=headers) as response:
                if response.status not in [200, 201]:
                    print("❌ 创建聊天会话失败")
                    return
                
                result = await response.json()
                chat_session_id = result.get("id")
                print(f"✅ 创建聊天会话成功 - ID: {chat_session_id}")
            
            # 发送基于知识库的问题
            chat_data = {
                "message": "什么是机器学习？请根据文档内容回答。",
                "model_id": "Qwen/Qwen2.5-7B-Instruct",
                "knowledge_base_ids": [kb_id]
            }
            
            print("🔄 发送基于知识库的问题...")
            async with session.post(f"{BASE_URL}/api/chat/sessions/{chat_session_id}/stream",
                                  json=chat_data, headers=headers) as response:
                if response.status == 200:
                    print("✅ 流式聊天连接成功")
                    
                    content_received = ""
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            try:
                                data = json.loads(line_str[6:])
                                if data.get('type') == 'content':
                                    content_received += data.get('content', '')
                                elif data.get('type') == 'error':
                                    print(f"⚠️ AI响应错误: {data.get('content')}")
                                elif data.get('type') == 'done':
                                    break
                            except json.JSONDecodeError:
                                continue
                    
                    if content_received:
                        print(f"✅ 接收到AI响应: {content_received[:200]}...")
                        if "机器学习" in content_received:
                            print("✅ AI响应包含相关知识库内容")
                        else:
                            print("⚠️ AI响应可能未使用知识库内容")
                    else:
                        print("⚠️ 未接收到AI响应内容")
                else:
                    text = await response.text()
                    print(f"❌ 流式聊天失败 - 状态码: {response.status}, 响应: {text}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        print("\n🎉 文档处理测试完成！")

if __name__ == "__main__":
    asyncio.run(test_document_processing())
