#!/usr/bin/env python3
import requests

# 先用管理员登录查看用户列表
login_data = {'username': 'admin', 'password': 'admin123'}
response = requests.post('http://localhost:8000/api/auth/login', json=login_data)
if response.status_code == 200:
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # 获取用户列表
    users_response = requests.get('http://localhost:8000/api/admin/users', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()
        print('现有用户:')
        for user in users:
            print(f'  - {user["username"]} (状态: {user["status"]})')
    else:
        print('获取用户列表失败')
else:
    print('管理员登录失败')
