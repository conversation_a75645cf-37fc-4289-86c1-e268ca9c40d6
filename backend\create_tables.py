#!/usr/bin/env python3
"""
创建数据库表脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import create_db_and_tables
from app.models import *  # 导入所有模型

def main():
    """创建数据库表"""
    print("开始创建数据库表...")
    
    try:
        create_db_and_tables()
        print("数据库表创建完成!")
    except Exception as e:
        print(f"创建数据库表失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
