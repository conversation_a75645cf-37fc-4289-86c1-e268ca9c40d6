"""
测试用户首页统计数据API
包括知识库数量、文档数量、聊天会话数量、存储使用等
"""
import requests
import tempfile
import os
import json

BASE_URL = "http://localhost:8000"

def test_stats_api():
    """测试统计数据API"""
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {"username": "testuser", "password": "test123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 创建一些测试数据
    print("\n📊 创建测试数据...")
    
    # 创建知识库
    kb_data = {"name": "统计测试知识库", "description": "用于测试统计API"}
    response = requests.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=headers)
    
    if response.status_code not in [200, 201]:
        print(f"❌ 创建知识库失败: {response.text}")
        return
    
    kb_id = response.json().get("id")
    print(f"✅ 创建知识库成功 - ID: {kb_id}")
    
    # 上传一些文档
    file_contents = [
        "这是统计测试文档1。包含关于数据分析的内容。",
        "这是统计测试文档2。包含关于机器学习的内容。"
    ]
    
    temp_files = []
    for i, content in enumerate(file_contents):
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=f'_stats_test_{i+1}.txt', delete=False, encoding='utf-8')
        temp_file.write(content)
        temp_file.close()
        temp_files.append(temp_file.name)
    
    try:
        # 批量上传
        files = []
        for temp_file in temp_files:
            files.append(('files', (os.path.basename(temp_file), open(temp_file, 'rb'), 'text/plain')))
        
        response = requests.post(f"{BASE_URL}/api/documents/batch-upload/{kb_id}", files=files, headers=headers)
        
        # 关闭文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上传文档成功 - {len(result['uploaded_documents'])} 个文档")
        else:
            print(f"❌ 上传文档失败: {response.text}")
    
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    # 创建聊天会话
    session_data = {"title": "统计测试聊天"}
    response = requests.post(f"{BASE_URL}/api/chat/sessions", json=session_data, headers=headers)
    
    if response.status_code not in [200, 201]:
        print(f"❌ 创建聊天会话失败: {response.text}")
        return
    
    chat_session_id = response.json().get("id")
    print(f"✅ 创建聊天会话成功 - ID: {chat_session_id}")
    
    # 发送一些消息
    chat_data = {
        "message": "这是一条测试消息",
        "model_id": "Qwen/Qwen2.5-7B-Instruct",
        "knowledge_base_ids": [kb_id]
    }
    
    response = requests.post(f"{BASE_URL}/api/chat/sessions/{chat_session_id}/stream", 
                           json=chat_data, headers=headers, stream=True)
    
    if response.status_code == 200:
        print("✅ 发送测试消息成功")
        # 简单读取响应
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        if data.get('type') == 'done':
                            break
                    except json.JSONDecodeError:
                        continue
    
    # 3. 测试基础统计API
    print("\n📈 测试基础统计API...")
    response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
    
    if response.status_code == 200:
        stats = response.json()
        print("✅ 获取基础统计数据成功:")
        print(f"   知识库数量: {stats['knowledge_bases']}")
        print(f"   文档数量: {stats['documents']}")
        print(f"   聊天会话数量: {stats['chat_sessions']}")
        print(f"   存储使用: {stats['storage_used']} GB / {stats['storage_total']} GB")
        print(f"   存储使用率: {stats['storage_percent']}%")
    else:
        print(f"❌ 获取基础统计数据失败: {response.text}")
    
    # 4. 测试详细统计API
    print("\n📊 测试详细统计API...")
    response = requests.get(f"{BASE_URL}/api/stats/detailed", headers=headers)
    
    if response.status_code == 200:
        detailed_stats = response.json()
        print("✅ 获取详细统计数据成功:")
        print(f"   知识库数量: {detailed_stats['knowledge_bases']}")
        print(f"   文档数量: {detailed_stats['documents']}")
        print(f"   聊天会话数量: {detailed_stats['chat_sessions']}")
        print(f"   消息总数: {detailed_stats['total_messages']}")
        print(f"   存储使用: {detailed_stats['storage_used_bytes']} bytes")
        print(f"   存储使用: {detailed_stats['storage_used_mb']} MB")
        print(f"   存储使用: {detailed_stats['storage_used_gb']} GB")
        print(f"   存储使用率: {detailed_stats['storage_percent']}%")
        print("   最近7天活动:")
        for key, value in detailed_stats['recent_activity'].items():
            print(f"     {key}: {value}")
    else:
        print(f"❌ 获取详细统计数据失败: {response.text}")
    
    # 5. 测试活动统计API
    print("\n📅 测试活动统计API...")
    response = requests.get(f"{BASE_URL}/api/stats/activity?days=7", headers=headers)
    
    if response.status_code == 200:
        activity_stats = response.json()
        print("✅ 获取活动统计数据成功:")
        print(f"   获取了最近 {len(activity_stats)} 天的活动数据")
        
        for day_stats in activity_stats[:3]:  # 只显示前3天
            print(f"   {day_stats['date']}:")
            print(f"     知识库创建: {day_stats['knowledge_bases_created']}")
            print(f"     文档上传: {day_stats['documents_uploaded']}")
            print(f"     会话创建: {day_stats['chat_sessions_created']}")
            print(f"     消息发送: {day_stats['messages_sent']}")
    else:
        print(f"❌ 获取活动统计数据失败: {response.text}")
    
    # 6. 测试不同天数的活动统计
    print("\n📈 测试不同天数的活动统计...")
    for days in [3, 7, 14]:
        response = requests.get(f"{BASE_URL}/api/stats/activity?days={days}", headers=headers)
        
        if response.status_code == 200:
            activity_stats = response.json()
            print(f"✅ 获取最近 {days} 天活动统计成功 - 共 {len(activity_stats)} 条记录")
        else:
            print(f"❌ 获取最近 {days} 天活动统计失败: {response.text}")
    
    # 7. 清理测试数据
    print("\n🧹 清理测试数据...")
    
    # 删除聊天会话
    response = requests.delete(f"{BASE_URL}/api/chat/sessions/{chat_session_id}", headers=headers)
    if response.status_code == 200:
        print("✅ 删除聊天会话成功")
    
    # 删除知识库
    response = requests.delete(f"{BASE_URL}/api/knowledge-bases/{kb_id}", headers=headers)
    if response.status_code == 200:
        print("✅ 删除知识库成功")
    
    print("\n🎉 统计API测试完成！")

if __name__ == "__main__":
    test_stats_api()
