#!/usr/bin/env python3
"""
测试所有修复的功能
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_registration_with_system_defaults():
    """测试用户注册时应用系统默认设置"""
    print("🧪 测试用户注册时应用系统默认设置...")
    
    # 创建测试用户
    test_user_data = {
        "username": f"testuser_{int(__import__('time').time())}",
        "email": f"test_{int(__import__('time').time())}@example.com",
        "password": "test123456",
        "display_name": "测试用户"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/register", json=test_user_data)
    if response.status_code == 200:
        user_data = response.json()
        print(f"  ✅ 用户注册成功: {user_data['username']}")
        
        # 登录获取token
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
            "username": test_user_data["username"],
            "password": test_user_data["password"]
        })
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # 检查用户设置是否应用了系统默认值
            settings_response = requests.get(f"{BASE_URL}/api/users/me/settings", headers=headers)
            if settings_response.status_code == 200:
                settings = settings_response.json()
                print(f"  ✅ 用户设置已创建，默认模型ID: {settings.get('default_model_id')}")
                
                # 检查用户配额是否应用了系统默认值
                quota_response = requests.get(f"{BASE_URL}/api/auth/quota", headers=headers)
                if quota_response.status_code == 200:
                    quota = quota_response.json()
                    print(f"  ✅ 用户配额已创建，存储配额: {quota.get('max_storage_mb')}MB")
                    return True
    
    print("  ❌ 用户注册测试失败")
    return False

def test_storage_calculation():
    """测试存储分布计算"""
    print("🧪 测试存储分布计算...")
    
    # 使用管理员账户登录
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 获取仪表盘统计
        stats_response = requests.get(f"{BASE_URL}/api/stats/dashboard", headers=headers)
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"  ✅ 存储统计: 已使用 {stats['storage_used']}GB / 总计 {stats['storage_total']}GB ({stats['storage_percent']}%)")
            return True
    
    print("  ❌ 存储计算测试失败")
    return False

def test_model_display_names():
    """测试模型显示名称"""
    print("🧪 测试模型显示名称...")

    # 使用管理员账户登录
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })

    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        # 获取AI模型列表
        response = requests.get(f"{BASE_URL}/api/ai/models", headers=headers)
        if response.status_code == 200:
            models = response.json()
            print(f"  ✅ 获取到 {len(models)} 个模型:")
            for model in models[:3]:  # 只显示前3个
                print(f"    - {model.get('display_name', model.get('model_name'))} (ID: {model['id']})")
            return True
        else:
            print(f"  ❌ 获取模型列表失败: {response.status_code} - {response.text}")
    else:
        print(f"  ❌ 管理员登录失败: {login_response.status_code} - {login_response.text}")

    return False

def test_system_settings():
    """测试系统设置"""
    print("🧪 测试系统设置...")
    
    # 使用管理员账户登录
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 获取系统设置
        settings_response = requests.get(f"{BASE_URL}/api/admin/settings", headers=headers)
        if settings_response.status_code == 200:
            settings = settings_response.json()
            print(f"  ✅ 获取到 {len(settings)} 个系统设置:")

            # 查找默认模型设置
            default_model_setting = next((s for s in settings if s['key'] == 'default_model_id'), None)
            if default_model_setting:
                print(f"    - 默认模型ID: {default_model_setting['value']}")

            # 查找默认存储配额设置
            storage_setting = next((s for s in settings if s['key'] == 'default_storage_quota'), None)
            if storage_setting:
                print(f"    - 默认存储配额: {storage_setting['value']}MB")

            return True
        else:
            print(f"  ❌ 获取系统设置失败: {settings_response.status_code} - {settings_response.text}")
    else:
        print(f"  ❌ 管理员登录失败: {login_response.status_code} - {login_response.text}")

    print("  ❌ 系统设置测试失败")
    return False

def main():
    """运行所有测试"""
    print("🔧 开始测试所有修复的功能...\n")
    
    tests = [
        ("系统设置", test_system_settings),
        ("模型显示名称", test_model_display_names),
        ("存储分布计算", test_storage_calculation),
        ("用户注册应用系统默认设置", test_user_registration_with_system_defaults),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"  ❌ {test_name} 测试出错: {e}")
            results.append((test_name, False))
            print()
    
    # 总结
    print("=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有修复都已验证成功！")
    else:
        print("⚠️  部分修复需要进一步检查")

if __name__ == "__main__":
    main()
