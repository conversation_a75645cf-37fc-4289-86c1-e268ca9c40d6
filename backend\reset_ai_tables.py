#!/usr/bin/env python3
"""
重置AI相关表结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import text
from sqlalchemy.orm import Session
from app.core.database import engine
from app.models import *  # 导入所有模型
from app.core.database import create_db_and_tables

def main():
    """重置AI相关表结构"""
    print("重置AI相关表结构...")
    
    try:
        with engine.connect() as conn:
            # 删除相关表（注意外键约束的顺序）
            print("删除旧表...")
            conn.execute(text("DROP TABLE IF EXISTS chat_messages CASCADE"))
            conn.execute(text("DROP TABLE IF EXISTS user_api_keys CASCADE"))
            conn.execute(text("DROP TABLE IF EXISTS ai_models CASCADE"))
            conn.execute(text("DROP TABLE IF EXISTS ai_providers CASCADE"))
            conn.commit()
            print("旧表删除完成")

    except Exception as e:
        print(f"删除表时出错: {e}")
        return 1
    
    # 重新创建表
    print("重新创建表...")

    try:
        create_db_and_tables()
        print("表重新创建完成!")
        return 0
    except Exception as e:
        print(f"创建表时出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
