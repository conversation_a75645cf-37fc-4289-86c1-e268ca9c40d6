<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full flex items-center justify-center">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载用户数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          用户管理
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          管理系统中的所有用户账户，调整权限和配额
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon class="mr-2"><Plus /></el-icon>
          创建用户
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- 搜索框 -->
        <div class="flex-1 max-w-md">
          <el-input
            v-model="searchQuery"
            placeholder="搜索用户名、邮箱..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 筛选器 -->
        <div class="flex items-center space-x-4">
          <!-- 状态筛选 -->
          <el-select v-model="statusFilter" placeholder="状态" style="width: 120px" @change="loadUsers">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="禁用" value="disabled" />
            <el-option label="待审核" value="pending" />
          </el-select>

          <!-- 刷新按钮 -->
          <el-button @click="loadUsers" :loading="loading">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow" v-loading="loading">
      <!-- 用户表格 -->
      <el-table :data="filteredUsers" style="width: 100%">
        <el-table-column prop="username" label="用户" min-width="200">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <el-avatar :size="40" :src="row.avatar_url">
                {{ row.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <div>
                <div class="font-medium text-gray-900 dark:text-gray-100">
                  {{ row.display_name || row.username }}
                </div>
                <div class="text-sm text-gray-500">
                  @{{ row.username }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" min-width="200">
          <template #default="{ row }">
            <span class="text-gray-600 dark:text-gray-400">
              {{ row.email }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="is_admin" label="角色" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_admin ? 'danger' : 'primary'" size="small">
              {{ row.is_admin ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'active' ? 'success' : row.status === 'disabled' ? 'danger' : 'warning'" 
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="last_login_at" label="最后登录" width="150">
          <template #default="{ row }">
            <span class="text-gray-500 dark:text-gray-400 text-sm">
              {{ row.last_login_at ? formatDate(row.last_login_at) : '从未登录' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="150">
          <template #default="{ row }">
            <span class="text-gray-500 dark:text-gray-400 text-sm">
              {{ formatDate(row.created_at) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center justify-center space-x-1">
              <el-button size="small" @click="editUser(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" @click="manageQuota(row)">
                <el-icon><Setting /></el-icon>
                配额
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleUserStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteUser(row)"
                :disabled="row.is_admin && row.id === currentUserId"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '创建用户'"
      width="600px"
      @close="resetUserForm"
    >
      <el-form :model="userForm" :rules="userFormRules" ref="userFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="!!editingUser"
          />
        </el-form-item>

        <el-form-item label="显示名称" prop="display_name">
          <el-input
            v-model="userForm.display_name"
            placeholder="请输入显示名称"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="userForm.email"
            placeholder="请输入邮箱"
            type="email"
          />
        </el-form-item>

        <el-form-item v-if="!editingUser" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            placeholder="请输入密码"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="角色" prop="is_admin">
          <el-radio-group v-model="userForm.is_admin">
            <el-radio :label="false">普通用户</el-radio>
            <el-radio :label="true">管理员</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="editingUser" label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio label="active">活跃</el-radio>
            <el-radio label="disabled">禁用</el-radio>
            <el-radio label="pending">待审核</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="saving">
            {{ editingUser ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 配额管理对话框 -->
    <el-dialog
      v-model="showQuotaDialog"
      title="配额管理"
      width="500px"
      @close="resetQuotaForm"
    >
      <el-form :model="quotaForm" :rules="quotaFormRules" ref="quotaFormRef" label-width="150px">
        <el-form-item label="最大知识库数量" prop="max_kbs">
          <el-input-number
            v-model="quotaForm.max_kbs"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="每个知识库最大文档数" prop="max_docs_per_kb">
          <el-input-number
            v-model="quotaForm.max_docs_per_kb"
            :min="1"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最大存储空间(MB)" prop="max_storage_mb">
          <el-input-number
            v-model="quotaForm.max_storage_mb"
            :min="100"
            :max="10000"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showQuotaDialog = false">取消</el-button>
          <el-button type="primary" @click="saveQuota" :loading="saving">
            更新配额
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Delete,
  Setting,
  Loading
} from '@element-plus/icons-vue'
import { adminAPI, type AdminUser, type CreateUserRequest, type UpdateUserRequest, type UserQuota, type UpdateUserQuotaRequest } from '@/api/admin'
import { useAuthStore } from '@/stores/auth'

// 状态管理
const authStore = useAuthStore()
const pageLoading = ref(true)
const loading = ref(false)
const saving = ref(false)

// 用户列表
const users = ref<AdminUser[]>([])
const searchQuery = ref('')
const statusFilter = ref('')

// 对话框状态
const showCreateDialog = ref(false)
const showQuotaDialog = ref(false)
const editingUser = ref<AdminUser | null>(null)
const currentQuotaUser = ref<AdminUser | null>(null)

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  display_name: '',
  is_admin: false,
  status: 'active'
})

const quotaForm = reactive({
  max_kbs: 5,
  max_docs_per_kb: 100,
  max_storage_mb: 1024
})

// 表单引用
const userFormRef = ref()
const quotaFormRef = ref()

// 当前用户ID
const currentUserId = computed(() => authStore.user?.id)

// 过滤后的用户列表
const filteredUsers = computed(() => {
  let filtered = users.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      (user.display_name && user.display_name.toLowerCase().includes(query))
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }

  return filtered
})

// 表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
  ]
}

const quotaFormRules = {
  max_kbs: [
    { required: true, message: '请输入最大知识库数量', trigger: 'blur' }
  ],
  max_docs_per_kb: [
    { required: true, message: '请输入每个知识库最大文档数', trigger: 'blur' }
  ],
  max_storage_mb: [
    { required: true, message: '请输入最大存储空间', trigger: 'blur' }
  ]
}

// 工具函数
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    disabled: '禁用',
    pending: '待审核'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
}

// 加载用户列表
const loadUsers = async () => {
  try {
    loading.value = true
    users.value = await adminAPI.getUsers()
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑用户
const editUser = (user: AdminUser) => {
  editingUser.value = user
  userForm.username = user.username
  userForm.email = user.email
  userForm.display_name = user.display_name || ''
  userForm.is_admin = user.is_admin
  userForm.status = user.status
  userForm.password = '' // 编辑时不显示密码
  showCreateDialog.value = true
}

// 管理配额
const manageQuota = async (user: AdminUser) => {
  try {
    currentQuotaUser.value = user
    const quota = await adminAPI.getUserQuota(user.id)
    quotaForm.max_kbs = quota.max_kbs
    quotaForm.max_docs_per_kb = quota.max_docs_per_kb
    quotaForm.max_storage_mb = quota.max_storage_mb
    showQuotaDialog.value = true
  } catch (error) {
    console.error('获取用户配额失败:', error)
    ElMessage.error('获取用户配额失败')
  }
}

// 切换用户状态
const toggleUserStatus = async (user: AdminUser) => {
  try {
    const newStatus = user.status === 'active' ? 'disabled' : 'active'
    const action = newStatus === 'active' ? '启用' : '禁用'

    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminAPI.updateUserStatus(user.id, { status: newStatus })
    user.status = newStatus
    ElMessage.success(`用户${action}成功`)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新用户状态失败:', error)
      ElMessage.error('更新用户状态失败')
    }
  }
}

// 删除用户
const deleteUser = async (user: AdminUser) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await adminAPI.deleteUser(user.id)
    await loadUsers() // 重新加载用户列表
    ElMessage.success('用户删除成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 保存用户
const saveUser = async () => {
  try {
    await userFormRef.value?.validate()
    saving.value = true

    if (editingUser.value) {
      // 更新用户
      const updateData: UpdateUserRequest = {
        username: userForm.username,
        email: userForm.email,
        display_name: userForm.display_name,
        is_admin: userForm.is_admin,
        status: userForm.status
      }
      await adminAPI.updateUser(editingUser.value.id, updateData)
      ElMessage.success('用户更新成功')
    } else {
      // 创建用户
      const createData: CreateUserRequest = {
        username: userForm.username,
        email: userForm.email,
        password: userForm.password,
        display_name: userForm.display_name,
        is_admin: userForm.is_admin
      }
      await adminAPI.createUser(createData)
      ElMessage.success('用户创建成功')
    }

    showCreateDialog.value = false
    await loadUsers()
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error('保存用户失败')
  } finally {
    saving.value = false
  }
}

// 保存配额
const saveQuota = async () => {
  try {
    await quotaFormRef.value?.validate()
    if (!currentQuotaUser.value) return

    saving.value = true
    const updateData: UpdateUserQuotaRequest = {
      max_kbs: quotaForm.max_kbs,
      max_docs_per_kb: quotaForm.max_docs_per_kb,
      max_storage_mb: quotaForm.max_storage_mb
    }

    await adminAPI.updateUserQuota(currentQuotaUser.value.id, updateData)
    ElMessage.success('配额更新成功')
    showQuotaDialog.value = false
  } catch (error) {
    console.error('更新配额失败:', error)
    ElMessage.error('更新配额失败')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetUserForm = () => {
  editingUser.value = null
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.display_name = ''
  userForm.is_admin = false
  userForm.status = 'active'
  userFormRef.value?.resetFields()
}

const resetQuotaForm = () => {
  currentQuotaUser.value = null
  quotaForm.max_kbs = 5
  quotaForm.max_docs_per_kb = 100
  quotaForm.max_storage_mb = 1024
  quotaFormRef.value?.resetFields()
}

// 页面初始化
onMounted(async () => {
  try {
    await loadUsers()
  } finally {
    pageLoading.value = false
  }
})
</script>
