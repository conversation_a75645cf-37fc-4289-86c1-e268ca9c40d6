"""
数据库初始化脚本
创建表并添加测试数据
"""
import asyncio
from datetime import datetime, timedelta
from sqlmodel import Session, select
from passlib.context import CryptContext
from app.core.database import engine, create_db_and_tables
from app.models import (
    User, UserQuota, KnowledgeBase, Document, 
    AIProvider, UserAPIKey, ChatSession, ChatMessage,
    OperationLog, SystemSetting
)
from app.config import settings

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

async def init_database():
    """初始化数据库"""
    print("正在创建数据库表...")
    create_db_and_tables()
    print("数据库表创建完成！")
    
    with Session(engine) as session:
        # 检查是否已有数据
        existing_users = session.exec(select(User)).all()
        if existing_users:
            print("数据库已有数据，跳过初始化")
            return
        
        print("正在添加测试数据...")
        
        # 1. 创建系统设置
        system_settings = [
            SystemSetting(key="allow_registration", value="true", description="是否允许用户注册"),
            SystemSetting(key="max_file_size", value="50", description="最大文件上传大小(MB)"),
            SystemSetting(key="default_storage_quota", value="1024", description="默认存储配额(MB)"),
        ]
        for setting in system_settings:
            session.add(setting)
        
        # 2. 创建AI提供商
        ai_providers = [
            AIProvider(
                provider_name="OpenAI",
                model_name="gpt-4",
                is_active=True,
                system_api_key=settings.openai_api_key,
                allow_system_key_use=bool(settings.openai_api_key)
            ),
            AIProvider(
                provider_name="OpenAI", 
                model_name="gpt-3.5-turbo",
                is_active=True,
                system_api_key=settings.openai_api_key,
                allow_system_key_use=bool(settings.openai_api_key)
            ),
            AIProvider(
                provider_name="Anthropic",
                model_name="claude-3-opus",
                is_active=True,
                system_api_key=settings.anthropic_api_key,
                allow_system_key_use=bool(settings.anthropic_api_key)
            ),
            AIProvider(
                provider_name="硅基流动",
                model_name="qwen-plus",
                is_active=True,
                system_api_key=settings.siliconflow_api_key,
                allow_system_key_use=True
            ),
            AIProvider(
                provider_name="硅基流动",
                model_name="qwen-turbo", 
                is_active=True,
                system_api_key=settings.siliconflow_api_key,
                allow_system_key_use=True
            ),
            AIProvider(
                provider_name="DeepSeek",
                model_name="deepseek-chat",
                is_active=False,
                system_api_key=settings.deepseek_api_key,
                allow_system_key_use=bool(settings.deepseek_api_key)
            ),
        ]
        for provider in ai_providers:
            session.add(provider)
        
        session.commit()  # 提交AI提供商数据，获取ID
        
        # 3. 创建测试用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=hash_password("admin123"),
            display_name="系统管理员",
            is_admin=True,
            status="active",
            last_login_at=datetime.utcnow()
        )
        session.add(admin_user)
        
        test_user = User(
            username="testuser",
            email="<EMAIL>", 
            password_hash=hash_password("test123"),
            display_name="测试用户",
            is_admin=False,
            status="active",
            last_login_at=datetime.utcnow()
        )
        session.add(test_user)
        
        session.commit()  # 提交用户数据，获取ID
        
        # 4. 创建用户配额
        admin_quota = UserQuota(
            user_id=admin_user.id,
            max_kbs=50,
            max_docs_per_kb=1000,
            max_storage_mb=10240
        )
        session.add(admin_quota)
        
        user_quota = UserQuota(
            user_id=test_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        
        # 5. 创建测试知识库
        kb1 = KnowledgeBase(
            owner_id=test_user.id,
            name="技术文档库",
            description="存储技术相关文档的知识库"
        )
        session.add(kb1)
        
        kb2 = KnowledgeBase(
            owner_id=test_user.id,
            name="产品需求文档",
            description="产品需求和设计文档"
        )
        session.add(kb2)
        
        session.commit()  # 提交知识库数据
        
        # 6. 创建测试文档
        doc1 = Document(
            kb_id=kb1.id,
            uploader_id=test_user.id,
            filename="API文档.pdf",
            storage_path="/uploads/api_doc.pdf",
            file_type="pdf",
            file_size=1024000,
            status="completed"
        )
        session.add(doc1)
        
        doc2 = Document(
            kb_id=kb1.id,
            uploader_id=test_user.id,
            filename="开发指南.docx",
            storage_path="/uploads/dev_guide.docx",
            file_type="docx",
            file_size=512000,
            status="completed"
        )
        session.add(doc2)
        
        session.commit()  # 提交文档数据
        
        # 7. 创建测试聊天会话
        chat_session = ChatSession(
            user_id=test_user.id,
            title="关于API使用的问题"
        )
        session.add(chat_session)
        session.commit()
        
        # 8. 创建测试聊天消息
        user_message = ChatMessage(
            session_id=chat_session.id,
            role="user",
            content="请帮我解释一下这个API的使用方法",
            referenced_kbs=[kb1.id]
        )
        session.add(user_message)
        
        # 获取硅基流动模型ID
        qwen_plus = session.exec(
            select(AIProvider).where(
                AIProvider.provider_name == "硅基流动",
                AIProvider.model_name == "qwen-plus"
            )
        ).first()
        
        assistant_message = ChatMessage(
            session_id=chat_session.id,
            role="assistant", 
            content="根据您的技术文档库，这个API的使用方法如下...",
            model_id_used=qwen_plus.id if qwen_plus else None,
            referenced_kbs=[kb1.id]
        )
        session.add(assistant_message)
        
        # 9. 创建操作日志
        log1 = OperationLog(
            user_id=test_user.id,
            action="create_knowledge_base",
            target_type="knowledge_base",
            target_id=kb1.id,
            details={"name": kb1.name},
            ip_address="127.0.0.1"
        )
        session.add(log1)
        
        session.commit()
        
        print("测试数据添加完成！")
        print(f"管理员账号: admin / admin123")
        print(f"测试用户账号: testuser / test123")
        print(f"创建了 {len(ai_providers)} 个AI模型")
        print(f"创建了 2 个知识库和 2 个文档")
        print(f"创建了 1 个聊天会话和 2 条消息")

if __name__ == "__main__":
    asyncio.run(init_database())
