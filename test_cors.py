#!/usr/bin/env python3
"""
测试CORS配置
"""
import requests

BASE_URL = "http://localhost:8000/api"

def test_cors_preflight():
    """测试CORS预检请求"""
    print("🔍 测试CORS预检请求...")
    
    # 测试OPTIONS请求
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "DELETE",
        "Access-Control-Request-Headers": "Authorization"
    }
    
    try:
        response = requests.options(f"{BASE_URL}/admin/ai-providers/6", headers=headers)
        print(f"OPTIONS请求状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ CORS预检请求成功")
        else:
            print("❌ CORS预检请求失败")
            
    except Exception as e:
        print(f"❌ CORS预检请求异常: {e}")

def test_delete_with_auth():
    """测试带认证的DELETE请求"""
    print("\n🔍 测试带认证的DELETE请求...")

    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return

        token = response.json()["access_token"]
        print("✅ 登录成功")

        # 测试DELETE请求
        headers = {
            "Authorization": f"Bearer {token}",
            "Origin": "http://localhost:3000"
        }

        # 先获取供应商列表
        response = requests.get(f"{BASE_URL}/admin/ai-providers", headers=headers)
        if response.status_code == 200:
            providers = response.json()
            print(f"✅ 获取供应商列表成功: {len(providers)} 个供应商")

            # 测试创建和删除供应商的完整流程
            print("\n🔧 测试创建和删除供应商...")

            # 创建测试供应商
            import time
            timestamp = int(time.time())
            test_provider = {
                "name": f"test_provider_{timestamp}",
                "display_name": f"测试供应商_{timestamp}",
                "base_url": "https://api.test.com",
                "description": "测试用供应商",
                "is_active": True
            }

            response = requests.post(f"{BASE_URL}/admin/ai-providers", json=test_provider, headers=headers)
            if response.status_code == 200:
                created_provider = response.json()
                print(f"✅ 供应商创建成功: {created_provider['display_name']}")

                # 删除刚创建的供应商
                response = requests.delete(f"{BASE_URL}/admin/ai-providers/{created_provider['id']}", headers=headers)
                if response.status_code == 200:
                    print("✅ 供应商删除成功")
                    print("✅ 前端DELETE功能应该可以正常工作")
                else:
                    print(f"❌ 供应商删除失败: {response.status_code} - {response.text}")
            else:
                print(f"❌ 供应商创建失败: {response.status_code} - {response.text}")

        else:
            print("❌ 获取供应商列表失败")

    except Exception as e:
        print(f"❌ DELETE请求异常: {e}")

if __name__ == "__main__":
    test_cors_preflight()
    test_delete_with_auth()
