#!/usr/bin/env python3
"""
测试前端API调用
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_frontend_api():
    """测试前端API调用"""
    print("🌐 测试前端API调用...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Origin": "http://localhost:3001"  # 模拟前端请求
        }
        
        # 1. 测试获取系统设置（前端会调用这个）
        print("\n📋 测试获取系统设置...")
        response = requests.get(f"{BASE_URL}/admin/settings", headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            settings = response.json()
            print("✅ 获取设置成功")
            
            # 检查关键设置
            settings_map = {s['key']: s['value'] for s in settings}
            print(f"  - default_model_id: {settings_map.get('default_model_id', 'Not found')}")
            print(f"  - chat_retention_days: {settings_map.get('chat_retention_days', 'Not found')}")
        else:
            print(f"❌ 获取设置失败: {response.text}")
            
        # 2. 测试获取AI模型列表（前端需要这个来显示模型名称）
        print("\n🤖 测试获取AI模型列表...")
        response = requests.get(f"{BASE_URL}/ai/models", headers=headers)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            models = response.json()
            print("✅ 获取模型列表成功")
            print(f"  - 模型数量: {len(models)}")
            for model in models[:3]:  # 显示前3个
                print(f"  - ID: {model['id']}, 名称: {model['display_name']}")
        else:
            print(f"❌ 获取模型列表失败: {response.text}")
            
        # 3. 测试CORS预检请求
        print("\n🔍 测试CORS预检请求...")
        options_headers = {
            "Origin": "http://localhost:3001",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "authorization,content-type"
        }
        response = requests.options(f"{BASE_URL}/admin/settings", headers=options_headers)
        print(f"OPTIONS状态码: {response.status_code}")
        print(f"CORS头部: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ CORS预检请求成功")
        else:
            print("❌ CORS预检请求失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_frontend_api()
