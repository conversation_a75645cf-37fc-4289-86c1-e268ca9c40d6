"""
AI相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel


# AI供应商相关Schema
class AIProviderBase(BaseModel):
    """AI供应商基础模式"""
    name: str
    display_name: str
    base_url: Optional[str] = None
    is_active: bool = True
    description: Optional[str] = None


class AIProviderCreate(AIProviderBase):
    """AI供应商创建模式"""
    pass


class AIProviderUpdate(BaseModel):
    """AI供应商更新模式"""
    display_name: Optional[str] = None
    base_url: Optional[str] = None
    is_active: Optional[bool] = None
    description: Optional[str] = None


class AIProviderResponse(AIProviderBase):
    """AI供应商响应模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# AI模型相关Schema
class AIModelBase(BaseModel):
    """AI模型基础模式"""
    model_name: str
    display_name: str
    is_active: bool = True
    system_api_key: Optional[str] = None
    allow_system_key_use: bool = False
    max_tokens: Optional[int] = 4000
    supports_streaming: bool = True
    cost_per_1k_tokens: Optional[float] = None


class AIModelCreate(AIModelBase):
    """AI模型创建模式"""
    provider_id: int


class AIModelUpdate(BaseModel):
    """AI模型更新模式"""
    display_name: Optional[str] = None
    is_active: Optional[bool] = None
    system_api_key: Optional[str] = None
    allow_system_key_use: Optional[bool] = None
    max_tokens: Optional[int] = None
    supports_streaming: Optional[bool] = None
    cost_per_1k_tokens: Optional[float] = None


class AIModelResponse(AIModelBase):
    """AI模型响应模式"""
    id: int
    provider_id: int
    provider: AIProviderResponse
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AIModelSimpleResponse(BaseModel):
    """AI模型简单响应模式（不包含provider详情）"""
    id: int
    provider_id: int
    model_name: str
    display_name: str
    is_active: bool
    allow_system_key_use: bool
    max_tokens: Optional[int]
    supports_streaming: bool
    cost_per_1k_tokens: Optional[float]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 用户API密钥相关Schema
class UserAPIKeyCreate(BaseModel):
    """用户API密钥创建模式"""
    provider_id: int
    api_key: str
    description: Optional[str] = None


class UserAPIKeyUpdate(BaseModel):
    """用户API密钥更新模式"""
    api_key: Optional[str] = None
    description: Optional[str] = None


class UserAPIKeyResponse(BaseModel):
    """用户API密钥响应模式"""
    id: int
    provider_id: int
    provider: AIProviderResponse
    description: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ChatSessionCreate(BaseModel):
    """聊天会话创建模式"""
    title: Optional[str] = None


class ChatSessionResponse(BaseModel):
    """聊天会话响应模式"""
    id: int
    user_id: int
    title: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ChatMessageCreate(BaseModel):
    """聊天消息创建模式"""
    content: str
    model_id: Optional[int] = None
    referenced_kbs: Optional[List[int]] = None


class ChatMessageResponse(BaseModel):
    """聊天消息响应模式"""
    id: int
    session_id: int
    role: str
    content: str
    model_id_used: Optional[int] = None
    referenced_kbs: Optional[List[int]] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """聊天请求模式"""
    message: str
    model_id: int  # 使用模型ID
    knowledge_base_ids: Optional[List[int]] = []
    history_limit: Optional[int] = 10  # 历史记录限制，默认10条
    relevance_threshold: Optional[float] = 0.7  # 相关度阈值，默认0.7


# 测试连接相关Schema
class TestConnectionRequest(BaseModel):
    """测试连接请求模式"""
    provider_id: int
    model_name: str
    api_key: Optional[str] = None  # 如果不提供，使用系统密钥


class TestConnectionResponse(BaseModel):
    """测试连接响应模式"""
    success: bool
    message: str
    response_time: Optional[float] = None
