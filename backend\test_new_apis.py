"""
测试新增的API功能
包括批量操作、聊天记录管理、模型选择等
"""
import requests
import tempfile
import os
import json

BASE_URL = "http://localhost:8000"

def test_new_apis():
    """测试新增的API功能"""
    
    # 1. 登录
    print("🔐 正在登录...")
    login_data = {"username": "testuser", "password": "test123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ 登录成功")
    
    # 2. 测试AI模型列表
    print("\n🤖 测试AI模型列表...")
    response = requests.get(f"{BASE_URL}/api/ai/models", headers=headers)
    if response.status_code == 200:
        models = response.json()
        print(f"✅ 获取AI模型列表成功 - 共 {len(models)} 个模型")
        for model in models:
            print(f"   - {model['provider_name']}: {model['model_name']}")
    else:
        print(f"❌ 获取AI模型列表失败: {response.text}")
        return
    
    # 3. 创建知识库
    print("\n📚 创建知识库...")
    kb_data = {"name": "新API测试知识库", "description": "测试新增API功能"}
    response = requests.post(f"{BASE_URL}/api/knowledge-bases/", json=kb_data, headers=headers)
    
    if response.status_code not in [200, 201]:
        print(f"❌ 创建知识库失败: {response.text}")
        return
    
    kb_id = response.json().get("id")
    print(f"✅ 创建知识库成功 - ID: {kb_id}")
    
    # 4. 测试批量文档上传
    print("\n📄 测试批量文档上传...")
    
    # 创建多个测试文档
    file_contents = [
        "Python编程语言：Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。",
        "机器学习基础：机器学习是人工智能的一个分支，使计算机能够从数据中学习模式。",
        "数据科学概述：数据科学结合了统计学、计算机科学和领域专业知识。"
    ]
    
    temp_files = []
    for i, content in enumerate(file_contents):
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=f'_batch_test_{i+1}.txt', delete=False, encoding='utf-8')
        temp_file.write(content)
        temp_file.close()
        temp_files.append(temp_file.name)
    
    try:
        # 批量上传
        files = []
        for temp_file in temp_files:
            files.append(('files', (os.path.basename(temp_file), open(temp_file, 'rb'), 'text/plain')))
        
        response = requests.post(f"{BASE_URL}/api/documents/batch-upload/{kb_id}", files=files, headers=headers)
        
        # 关闭文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 批量上传成功")
            print(f"   成功上传: {len(result['uploaded_documents'])} 个文档")
            print(f"   失败: {len(result['failed_uploads'])} 个文档")
        else:
            print(f"❌ 批量上传失败: {response.text}")
            return
    
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    # 5. 测试创建聊天会话
    print("\n💬 测试聊天会话管理...")
    session_data = {"title": "新API测试聊天"}
    response = requests.post(f"{BASE_URL}/api/chat/sessions", json=session_data, headers=headers)
    
    if response.status_code not in [200, 201]:
        print(f"❌ 创建聊天会话失败: {response.text}")
        return
    
    chat_session_id = response.json().get("id")
    print(f"✅ 创建聊天会话成功 - ID: {chat_session_id}")
    
    # 6. 测试多知识库聊天（带历史记录限制）
    print("\n🔄 测试多知识库聊天（带历史记录限制）...")
    chat_data = {
        "message": "请介绍一下Python编程语言的特点",
        "model_id": "Qwen/Qwen2.5-7B-Instruct",
        "knowledge_base_ids": [kb_id],
        "history_limit": 3
    }
    
    response = requests.post(f"{BASE_URL}/api/chat/sessions/{chat_session_id}/stream", 
                           json=chat_data, headers=headers, stream=True)
    
    if response.status_code == 200:
        print("✅ 多知识库聊天连接成功")
        
        content_received = ""
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        if data.get('type') == 'content':
                            content = data.get('content', '')
                            content_received += content
                            print(content, end='', flush=True)
                        elif data.get('type') == 'error':
                            print(f"\n⚠️ AI响应错误: {data.get('content')}")
                        elif data.get('type') == 'done':
                            break
                    except json.JSONDecodeError:
                        continue
        
        print(f"\n✅ AI响应完成，共接收 {len(content_received)} 个字符")
    else:
        print(f"❌ 多知识库聊天失败: {response.text}")
    
    # 7. 再发送一条消息
    print("\n🔄 发送第二条消息...")
    chat_data2 = {
        "message": "机器学习和数据科学有什么关系？",
        "model_id": "Qwen/Qwen2.5-7B-Instruct",
        "knowledge_base_ids": [kb_id],
        "history_limit": 5
    }
    
    response = requests.post(f"{BASE_URL}/api/chat/sessions/{chat_session_id}/stream", 
                           json=chat_data2, headers=headers, stream=True)
    
    if response.status_code == 200:
        print("✅ 第二条消息发送成功")
        
        content_received = ""
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        if data.get('type') == 'content':
                            content = data.get('content', '')
                            content_received += content
                            print(content, end='', flush=True)
                        elif data.get('type') == 'done':
                            break
                    except json.JSONDecodeError:
                        continue
        
        print(f"\n✅ 第二条AI响应完成")
    
    # 8. 测试获取聊天历史（限制数量）
    print("\n📜 测试聊天历史获取（限制数量）...")
    response = requests.get(f"{BASE_URL}/api/chat/sessions/{chat_session_id}/messages/history?limit=3", 
                          headers=headers)
    
    if response.status_code == 200:
        history = response.json()
        print(f"✅ 获取聊天历史成功")
        print(f"   消息数量: {history['total_count']}")
        print(f"   是否限制: {history['limited']}")
        
        # 获取第一条AI消息用于重新生成测试
        ai_message_id = None
        for msg in history['messages']:
            if msg['role'] == 'assistant':
                ai_message_id = msg['id']
                print(f"   找到AI消息ID: {ai_message_id}")
                break
    else:
        print(f"❌ 获取聊天历史失败: {response.text}")
        ai_message_id = None
    
    # 9. 测试消息重新生成
    if ai_message_id:
        print("\n🔄 测试消息重新生成...")
        regenerate_data = {
            "message_id": ai_message_id,
            "model_id": "Qwen/Qwen2.5-7B-Instruct",
            "knowledge_base_ids": [kb_id]
        }
        
        response = requests.post(f"{BASE_URL}/api/chat/messages/regenerate", 
                               json=regenerate_data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 消息重新生成成功")
            print(f"   新内容长度: {len(result['content'])} 字符")
        else:
            print(f"❌ 消息重新生成失败: {response.text}")
    
    # 10. 测试文档列表和批量删除
    print("\n📋 测试文档列表...")
    response = requests.get(f"{BASE_URL}/api/documents/kb/{kb_id}", headers=headers)
    
    if response.status_code == 200:
        documents = response.json()
        print(f"✅ 获取文档列表成功 - 共 {len(documents)} 个文档")
        
        # 获取文档ID用于批量删除测试
        doc_ids_to_delete = [doc['id'] for doc in documents[:2]]  # 删除前两个文档
        
        if doc_ids_to_delete:
            print(f"\n🗑️ 测试批量删除文档（删除ID: {doc_ids_to_delete}）...")
            delete_data = {"document_ids": doc_ids_to_delete}
            response = requests.delete(f"{BASE_URL}/api/documents/batch-delete",
                                     json=delete_data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 批量删除文档成功")
                print(f"   删除成功: {result['deleted_count']} 个文档")
                print(f"   删除失败: {len(result['failed_deletes'])} 个文档")
            else:
                print(f"❌ 批量删除文档失败: {response.text}")
    else:
        print(f"❌ 获取文档列表失败: {response.text}")
    
    # 11. 清理测试数据
    print("\n🧹 清理测试数据...")
    
    # 删除聊天会话
    response = requests.delete(f"{BASE_URL}/api/chat/sessions/{chat_session_id}", headers=headers)
    if response.status_code == 200:
        print("✅ 删除聊天会话成功")
    
    # 删除知识库
    response = requests.delete(f"{BASE_URL}/api/knowledge-bases/{kb_id}", headers=headers)
    if response.status_code == 200:
        print("✅ 删除知识库成功")
    
    print("\n🎉 所有新增API测试完成！")

if __name__ == "__main__":
    test_new_apis()
