import request from './request'
import type { UserInfo } from './auth'

// 管理员相关接口类型
export interface SystemStats {
  total_users: number
  total_knowledge_bases: number
  total_documents: number
  total_chat_sessions: number
  total_messages: number
  active_users_today: number
  total_storage_mb: number
}

export interface AdminUser extends UserInfo {
  last_login_at?: string
}

export interface UpdateUserStatusRequest {
  status: 'active' | 'disabled' | 'pending'
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  display_name?: string
  is_admin: boolean
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  display_name?: string
  is_admin?: boolean
  status?: 'active' | 'disabled' | 'pending'
}

export interface UserQuota {
  user_id: number
  max_kbs: number
  max_docs_per_kb: number
  max_storage_mb: number
  updated_at: string
}

export interface UpdateUserQuotaRequest {
  max_kbs?: number
  max_docs_per_kb?: number
  max_storage_mb?: number
}

export interface AdminUserListParams {
  offset?: number
  limit?: number
  status_filter?: string
}

export interface AIProvider {
  id: number
  name: string
  display_name: string
  base_url?: string
  is_active: boolean
  description?: string
  created_at: string
  updated_at: string
}

export interface CreateAIProviderRequest {
  name: string
  display_name: string
  base_url?: string
  description?: string
  is_active: boolean
}

export interface AIModelAdmin {
  id: number
  provider_id: number
  model_name: string
  display_name: string
  is_active: boolean
  system_api_key?: string
  allow_system_key_use: boolean
  max_tokens?: number
  supports_streaming: boolean
  cost_per_1k_tokens?: number
  created_at: string
  updated_at: string
}

export interface CreateAIModelRequest {
  provider_id: number
  model_name: string
  display_name: string
  is_active: boolean
  system_api_key?: string
  allow_system_key_use: boolean
  max_tokens?: number
  supports_streaming: boolean
  cost_per_1k_tokens?: number
}

export interface UpdateAIModelRequest {
  display_name?: string
  is_active?: boolean
  system_api_key?: string
  allow_system_key_use?: boolean
  max_tokens?: number | null
  supports_streaming?: boolean
  cost_per_1k_tokens?: number | null
}

export interface OperationLog {
  id: number
  user_id: number
  action: string
  target_type: string
  target_id?: number
  details?: string
  ip_address?: string
  created_at: string
}

export interface LogListParams {
  offset?: number
  limit?: number
  action_filter?: string
}

export interface SystemSetting {
  key: string
  value: string
  description?: string
  updated_at: string
}

export interface UpdateSettingRequest {
  value: string
  description?: string
}

// 管理员API
export const adminAPI = {
  // 获取系统统计
  getSystemStats: (): Promise<SystemStats> => {
    return request.get('/admin/stats')
  },

  // 获取所有用户
  getUsers: (params?: AdminUserListParams): Promise<AdminUser[]> => {
    return request.get('/admin/users', { params })
  },

  // 创建用户
  createUser: (data: CreateUserRequest): Promise<AdminUser> => {
    return request.post('/admin/users', data)
  },

  // 更新用户信息
  updateUser: (userId: number, data: UpdateUserRequest): Promise<AdminUser> => {
    return request.put(`/admin/users/${userId}`, data)
  },

  // 删除用户
  deleteUser: (userId: number): Promise<{ message: string }> => {
    return request.delete(`/admin/users/${userId}`)
  },

  // 更新用户状态
  updateUserStatus: (userId: number, data: UpdateUserStatusRequest): Promise<{ message: string }> => {
    return request.put(`/admin/users/${userId}/status`, data)
  },

  // 获取用户配额
  getUserQuota: (userId: number): Promise<UserQuota> => {
    return request.get(`/admin/users/${userId}/quota`)
  },

  // 更新用户配额
  updateUserQuota: (userId: number, data: UpdateUserQuotaRequest): Promise<UserQuota> => {
    return request.put(`/admin/users/${userId}/quota`, data)
  },

  // 获取AI提供商列表
  getAIProviders: (): Promise<AIProvider[]> => {
    return request.get('/admin/ai-providers')
  },

  // 创建AI提供商
  createAIProvider: (data: CreateAIProviderRequest): Promise<AIProvider> => {
    return request.post('/admin/ai-providers', data)
  },

  // 删除AI提供商
  deleteAIProvider: (providerId: number): Promise<{ message: string }> => {
    return request.delete(`/admin/ai-providers/${providerId}`)
  },

  // 获取AI模型列表
  getAIModels: (providerId?: number): Promise<AIModelAdmin[]> => {
    const params = providerId ? { provider_id: providerId } : undefined
    return request.get('/admin/ai-models', { params })
  },

  // 创建AI模型
  createAIModel: (data: CreateAIModelRequest): Promise<AIModelAdmin> => {
    return request.post('/admin/ai-models', data)
  },

  // 更新AI模型配置
  updateAIModel: (modelId: number, data: UpdateAIModelRequest): Promise<AIModelAdmin> => {
    return request.put(`/admin/ai-models/${modelId}`, data)
  },

  // 删除AI模型
  deleteAIModel: (modelId: number): Promise<{ message: string }> => {
    return request.delete(`/admin/ai-models/${modelId}`)
  },

  // 获取操作日志
  getLogs: (params?: LogListParams): Promise<OperationLog[]> => {
    return request.get('/admin/logs', { params })
  },

  // 获取系统设置
  getSettings: (): Promise<SystemSetting[]> => {
    return request.get('/admin/settings')
  },

  // 更新系统设置
  updateSetting: (settingKey: string, data: UpdateSettingRequest): Promise<{ message: string }> => {
    return request.put(`/admin/settings/${settingKey}`, data)
  }
}
