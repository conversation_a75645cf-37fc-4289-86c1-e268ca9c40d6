#!/usr/bin/env python3
"""
最终测试：验证前端删除功能
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def final_test():
    """最终测试"""
    print("🎯 最终测试：验证前端删除功能...")
    
    # 先登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
            
        token = response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Origin": "http://localhost:3000"
        }
        
        # 1. 创建测试供应商
        print("\n📝 创建测试供应商...")
        import time
        timestamp = int(time.time())
        test_provider = {
            "name": f"test_provider_{timestamp}",
            "display_name": f"测试供应商_{timestamp}",
            "base_url": "https://api.test.com",
            "description": "前端删除功能测试用供应商",
            "is_active": True
        }
        
        response = requests.post(f"{BASE_URL}/admin/ai-providers", json=test_provider, headers=headers)
        if response.status_code == 200:
            created_provider = response.json()
            print(f"✅ 供应商创建成功: {created_provider['display_name']} (ID: {created_provider['id']})")
            
            # 2. 测试前端删除功能
            print(f"\n🗑️ 测试删除供应商 ID: {created_provider['id']}")
            
            # 模拟前端的DELETE请求
            delete_response = requests.delete(
                f"{BASE_URL}/admin/ai-providers/{created_provider['id']}", 
                headers=headers
            )
            
            print(f"DELETE请求状态码: {delete_response.status_code}")
            print(f"DELETE请求响应: {delete_response.text}")
            
            if delete_response.status_code == 200:
                print("✅ 前端删除功能正常工作！")
                print("✅ CORS问题已解决！")
                print("✅ 所有功能都可以正常使用！")
            else:
                print("❌ 删除失败")
                
        else:
            print(f"❌ 供应商创建失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    final_test()
